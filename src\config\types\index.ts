/**
 * Konfigürasyon Tipleri - Ana Export Dosyası
 * 
 * Bu dosya tüm konfigürasyon tiplerini ve arayüzlerini dışa aktarır.
 * Merkezi import noktası sağlar.
 * 
 * <AUTHOR> Architecture Team
 * @version 1.0.0
 */

// ============================================================================
// TÜM KONFİGÜRASYON TİPLERİNİ EXPORT ET
// ============================================================================

export type {
  // Temel Tipler
  DataSource,
  LogLevel,
  NotificationProvider,
  CacheProvider,
  Environment,
  LoggerProvider,
  EncryptionAlgorithm,

  // Uygulama Konfigürasyonu
  AppFeatures,
  AppConfig,

  // API Konfigürasyonu
  CircuitBreakerConfig,
  RateLimitingConfig,
  WebSocketConfig,
  ApiConfig,

  // Güvenlik Konfigürasyonu
  AuthConfig,
  EncryptionConfig,
  CsrfConfig,
  CorsConfig,
  SecurityConfig,

  // Logger Konfigürasyonu
  LoggerConfig,

  // Firebase Konfigürasyonu
  FirebaseConfig,

  // Bildirim Konfigürasyonu
  PushNotificationConfig,
  EmailNotificationConfig,
  NotificationConfig,

  // Önbellek Konfigürasyonu
  RedisConfig,
  CacheConfig,

  // İzleme Konfigürasyonu
  PerformanceMonitoringConfig,
  ErrorMonitoringConfig,
  AnalyticsConfig,
  MonitoringConfig,

  // Ana Konfigürasyon
  CoreConfig,

  // Doğrulama Tipleri
  ValidationError,
  ValidationResult,

  // Yardımcı Tipler
  ConfigFactory,
  ConfigValidator,
  EnvValue,
  EnvMap,
} from './config.types';

// ============================================================================
// TİP KORUYUCULARI (Type Guards)
// ============================================================================

/**
 * DataSource tipini kontrol eden tip koruyucusu
 */
export const isDataSource = (value: unknown): value is DataSource => {
  return typeof value === 'string' && 
    ['restapi', 'firebase', 'graphql', 'hybrid'].includes(value);
};

/**
 * Environment tipini kontrol eden tip koruyucusu
 */
export const isEnvironment = (value: unknown): value is Environment => {
  return typeof value === 'string' && 
    ['development', 'staging', 'production'].includes(value);
};

/**
 * LogLevel tipini kontrol eden tip koruyucusu
 */
export const isLogLevel = (value: unknown): value is LogLevel => {
  return typeof value === 'string' && 
    ['debug', 'info', 'warn', 'error'].includes(value);
};

/**
 * NotificationProvider tipini kontrol eden tip koruyucusu
 */
export const isNotificationProvider = (value: unknown): value is NotificationProvider => {
  return typeof value === 'string' && 
    ['quasar', 'push', 'email', 'multiple'].includes(value);
};

/**
 * CacheProvider tipini kontrol eden tip koruyucusu
 */
export const isCacheProvider = (value: unknown): value is CacheProvider => {
  return typeof value === 'string' && 
    ['memory', 'localStorage', 'redis', 'hybrid'].includes(value);
};

/**
 * LoggerProvider tipini kontrol eden tip koruyucusu
 */
export const isLoggerProvider = (value: unknown): value is LoggerProvider => {
  return typeof value === 'string' && 
    ['console', 'remote', 'file', 'multiple'].includes(value);
};

// ============================================================================
// VARSAYILAN DEĞERLER (Default Values)
// ============================================================================

/**
 * Varsayılan uygulama özellikleri
 */
export const DEFAULT_APP_FEATURES: AppFeatures = {
  realTime: false,
  offline: true,
  analytics: false,
  monitoring: false,
  pwa: true,
} as const;

/**
 * Varsayılan devre kesici konfigürasyonu
 */
export const DEFAULT_CIRCUIT_BREAKER_CONFIG: CircuitBreakerConfig = {
  enabled: true,
  failureThreshold: 5,
  resetTimeout: 60000, // 1 dakika
  monitoringPeriod: 10000, // 10 saniye
} as const;

/**
 * Varsayılan hız sınırlama konfigürasyonu
 */
export const DEFAULT_RATE_LIMITING_CONFIG: RateLimitingConfig = {
  enabled: true,
  maxRequests: 100,
  windowMs: 60000, // 1 dakika
  retryAfter: 1000, // 1 saniye
} as const;

/**
 * Varsayılan WebSocket konfigürasyonu
 */
export const DEFAULT_WEBSOCKET_CONFIG: WebSocketConfig = {
  enabled: false,
  url: '',
  reconnectInterval: 5000, // 5 saniye
  maxReconnectAttempts: 10,
  heartbeatInterval: 30000, // 30 saniye
} as const;

/**
 * Varsayılan kimlik doğrulama konfigürasyonu
 */
export const DEFAULT_AUTH_CONFIG: AuthConfig = {
  tokenExpiry: 3600000, // 1 saat
  refreshTokenExpiry: 604800000, // 1 hafta
  sessionTimeout: 1800000, // 30 dakika
  maxLoginAttempts: 5,
  lockoutDuration: 900000, // 15 dakika
} as const;

/**
 * Varsayılan şifreleme konfigürasyonu
 */
export const DEFAULT_ENCRYPTION_CONFIG: EncryptionConfig = {
  algorithm: 'AES-256-GCM',
  keySize: 256,
  saltRounds: 12,
  ivSize: 16,
} as const;

/**
 * Varsayılan CSRF konfigürasyonu
 */
export const DEFAULT_CSRF_CONFIG: CsrfConfig = {
  enabled: true,
  cookieName: '_csrf',
  secure: true,
  sameSite: 'strict',
} as const;

/**
 * Varsayılan CORS konfigürasyonu
 */
export const DEFAULT_CORS_CONFIG: CorsConfig = {
  enabled: true,
  origins: ['http://localhost:9000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
} as const;

/**
 * Varsayılan Redis konfigürasyonu
 */
export const DEFAULT_REDIS_CONFIG: RedisConfig = {
  host: 'localhost',
  port: 6379,
  db: 0,
  connectTimeout: 5000, // 5 saniye
} as const;

/**
 * Varsayılan performans izleme konfigürasyonu
 */
export const DEFAULT_PERFORMANCE_MONITORING_CONFIG: PerformanceMonitoringConfig = {
  enabled: false,
  sampleRate: 0.1, // %10
  collectInterval: 5000, // 5 saniye
} as const;

/**
 * Varsayılan hata izleme konfigürasyonu
 */
export const DEFAULT_ERROR_MONITORING_CONFIG: ErrorMonitoringConfig = {
  enabled: true,
  maxReportSize: 1024 * 1024, // 1MB
} as const;

/**
 * Varsayılan analitik konfigürasyonu
 */
export const DEFAULT_ANALYTICS_CONFIG: AnalyticsConfig = {
  enabled: false,
  provider: 'google',
  sampleRate: 1.0, // %100
} as const;

// ============================================================================
// YARDIMCI FONKSİYONLAR (Utility Functions)
// ============================================================================

/**
 * Ortam değişkenini boolean'a dönüştürür
 */
export const parseEnvBoolean = (value: string | undefined, defaultValue: boolean = false): boolean => {
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === 'true';
};

/**
 * Ortam değişkenini number'a dönüştürür
 */
export const parseEnvNumber = (value: string | undefined, defaultValue: number = 0): number => {
  if (value === undefined) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

/**
 * Ortam değişkenini string array'e dönüştürür
 */
export const parseEnvArray = (value: string | undefined, separator: string = ','): string[] => {
  if (!value) return [];
  return value.split(separator).map(item => item.trim()).filter(Boolean);
};

/**
 * Konfigürasyon nesnesini dondurur (immutable yapar)
 */
export const freezeConfig = <T>(config: T): Readonly<T> => {
  return Object.freeze(config);
};
