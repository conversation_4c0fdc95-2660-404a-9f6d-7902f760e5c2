# Core Composables Sistemi

## Ana Core Composable

```typescript
// src/core/composables/useCore.ts
export const useCore = () => {
  const config = inject<CoreConfig>('coreConfig');
  const logger = inject<LoggerService>('logger');
  const api = inject<ApiService>('api');
  const notification = inject<NotificationService>('notification');
  const errorHandler = inject<ErrorHandlerService>('errorHandler');
  const cache = inject<CacheService>('cache');
  const eventBus = inject<EventBusService>('eventBus');
  const performance = inject<PerformanceMonitor>('performance');

  if (!config || !logger || !api || !notification || !errorHandler) {
    throw new Error('Core services not properly injected');
  }

  // Global error handling
  const handleError = async (error: unknown, context?: any) => {
    const appError = await errorHandler.handle(error, context);
    await errorHandler.report(appError);
    const recovered = await errorHandler.recover(appError);

    if (!recovered) {
      logger.error('Unrecoverable error occurred', appError, 'CoreComposable');
    }

    return appError;
  };

  // Safe async operation wrapper
  const safeAsync = async <T>(
    operation: () => Promise<T>,
    context?: any,
  ): Promise<Either<AppError, T>> => {
    try {
      const result = await operation();
      return Either.right<AppError, T>(result);
    } catch (error) {
      const appError = await handleError(error, context);
      return Either.left<AppError, T>(appError);
    }
  };

  return {
    config,
    logger,
    api,
    notification,
    errorHandler,
    cache,
    eventBus,
    performance,
    handleError,
    safeAsync,
  };
};
```

## API Composable

```typescript
// src/core/composables/useApi.ts
export interface ApiOptions {
  timeout?: number;
  retries?: number;
  cache?: boolean;
  cacheTtl?: number;
}

export const useApi = () => {
  const { api, cache, logger, handleError } = useCore();

  // GET request with caching
  const get = async <T>(url: string, options: ApiOptions = {}): Promise<Either<AppError, T>> => {
    const cacheKey = `api:get:${url}:${JSON.stringify(options)}`;

    // Check cache first
    if (options.cache && cache) {
      const cached = await cache.get<T>(cacheKey);
      if (cached) {
        logger.debug(`Cache hit for ${url}`, { url, cacheKey }, 'ApiComposable');
        return Either.right<AppError, T>(cached);
      }
    }

    return safeAsync(
      async () => {
        const result = await api.get<T>(url, {
          timeout: options.timeout,
          retries: options.retries,
        });

        // Cache the result
        if (options.cache && cache) {
          await cache.set(cacheKey, result, options.cacheTtl);
        }

        return result;
      },
      { operation: 'api.get', url },
    );
  };

  // POST request
  const post = async <T, D = any>(
    url: string,
    data: D,
    options: ApiOptions = {},
  ): Promise<Either<AppError, T>> => {
    return safeAsync(
      async () => {
        return await api.post<T>(url, data, {
          timeout: options.timeout,
          retries: options.retries,
        });
      },
      { operation: 'api.post', url, data },
    );
  };

  // PUT request
  const put = async <T, D = any>(
    url: string,
    data: D,
    options: ApiOptions = {},
  ): Promise<Either<AppError, T>> => {
    return safeAsync(
      async () => {
        return await api.put<T>(url, data, {
          timeout: options.timeout,
          retries: options.retries,
        });
      },
      { operation: 'api.put', url, data },
    );
  };

  // DELETE request
  const del = async <T>(url: string, options: ApiOptions = {}): Promise<Either<AppError, T>> => {
    return safeAsync(
      async () => {
        return await api.delete<T>(url, {
          timeout: options.timeout,
          retries: options.retries,
        });
      },
      { operation: 'api.delete', url },
    );
  };

  // Batch requests
  const batch = async <T>(requests: Array<() => Promise<T>>): Promise<Either<AppError, T[]>> => {
    return safeAsync(
      async () => {
        return await Promise.all(requests.map((req) => req()));
      },
      { operation: 'api.batch', count: requests.length },
    );
  };

  const { safeAsync } = useCore();

  return {
    get,
    post,
    put,
    delete: del,
    batch,
  };
};
```

## Logger Composable

```typescript
// src/core/composables/useLogger.ts
export const useLogger = (component?: string) => {
  const { logger } = useCore();
  const componentName = component || getCurrentInstance()?.type.name || 'Unknown';

  const debug = (message: string, data?: any) => {
    logger.debug(message, data, componentName);
  };

  const info = (message: string, data?: any) => {
    logger.info(message, data, componentName);
  };

  const warn = (message: string, data?: any) => {
    logger.warn(message, data, componentName);
  };

  const error = (message: string, error?: any) => {
    logger.error(message, error, componentName);
  };

  // Performance logging
  const time = (label: string) => {
    const startTime = performance.now();
    return () => {
      const duration = performance.now() - startTime;
      logger.info(`${label} completed in ${duration.toFixed(2)}ms`, { duration }, componentName);
    };
  };

  // Structured logging
  const logEvent = (eventName: string, data?: any) => {
    logger.info(`Event: ${eventName}`, { event: eventName, ...data }, componentName);
  };

  return {
    debug,
    info,
    warn,
    error,
    time,
    logEvent,
  };
};
```

## Notification Composable

```typescript
// src/core/composables/useNotification.ts
export interface NotificationOptions {
  type?: 'success' | 'error' | 'warning' | 'info';
  timeout?: number;
  persistent?: boolean;
  actions?: Array<{
    label: string;
    handler: () => void;
  }>;
}

export const useNotification = () => {
  const { notification } = useCore();

  const show = (message: string, options: NotificationOptions = {}) => {
    notification.show({
      type: options.type || 'info',
      message,
      timeout: options.timeout,
      persistent: options.persistent,
      actions: options.actions,
    });
  };

  const success = (message: string, options?: Omit<NotificationOptions, 'type'>) => {
    show(message, { ...options, type: 'success' });
  };

  const error = (message: string, options?: Omit<NotificationOptions, 'type'>) => {
    show(message, { ...options, type: 'error', persistent: true });
  };

  const warning = (message: string, options?: Omit<NotificationOptions, 'type'>) => {
    show(message, { ...options, type: 'warning' });
  };

  const info = (message: string, options?: Omit<NotificationOptions, 'type'>) => {
    show(message, { ...options, type: 'info' });
  };

  // Loading notification
  const loading = (message: string = 'Yükleniyor...') => {
    const id = notification.show({
      type: 'info',
      message,
      persistent: true,
      loading: true,
    });

    return {
      dismiss: () => notification.dismiss(id),
      update: (newMessage: string) => notification.update(id, { message: newMessage }),
    };
  };

  return {
    show,
    success,
    error,
    warning,
    info,
    loading,
  };
};
```

## Cache Composable

```typescript
// src/core/composables/useCache.ts
export const useCache = () => {
  const { cache, logger } = useCore();

  if (!cache) {
    throw new Error('Cache service not available');
  }

  // Reactive cache
  const cachedRef = <T>(key: string, fetcher: () => Promise<T>, ttl?: number) => {
    const data = ref<T | null>(null);
    const loading = ref(false);
    const error = ref<Error | null>(null);

    const load = async () => {
      loading.value = true;
      error.value = null;

      try {
        // Try cache first
        const cached = await cache.get<T>(key);
        if (cached) {
          data.value = cached;
          loading.value = false;
          return;
        }

        // Fetch fresh data
        const fresh = await fetcher();
        data.value = fresh;

        // Cache the result
        await cache.set(key, fresh, ttl);

        logger.debug(`Data cached for key: ${key}`, { key, ttl }, 'CacheComposable');
      } catch (err) {
        error.value = err as Error;
        logger.error(`Failed to load data for key: ${key}`, err, 'CacheComposable');
      } finally {
        loading.value = false;
      }
    };

    const refresh = async () => {
      await cache.delete(key);
      await load();
    };

    // Auto-load on mount
    onMounted(load);

    return {
      data: readonly(data),
      loading: readonly(loading),
      error: readonly(error),
      refresh,
    };
  };

  // Simple cache operations
  const get = async <T>(key: string): Promise<T | null> => {
    return await cache.get<T>(key);
  };

  const set = async <T>(key: string, value: T, ttl?: number): Promise<void> => {
    await cache.set(key, value, ttl);
  };

  const del = async (key: string): Promise<void> => {
    await cache.delete(key);
  };

  const clear = async (): Promise<void> => {
    await cache.clear();
  };

  // Cache with fallback
  const getOrSet = async <T>(key: string, fetcher: () => Promise<T>, ttl?: number): Promise<T> => {
    const cached = await cache.get<T>(key);
    if (cached) {
      return cached;
    }

    const fresh = await fetcher();
    await cache.set(key, fresh, ttl);
    return fresh;
  };

  return {
    cachedRef,
    get,
    set,
    delete: del,
    clear,
    getOrSet,
  };
};
```

## Event Bus Composable

```typescript
// src/core/composables/useEvents.ts
export const useEvents = () => {
  const { eventBus } = useCore();

  if (!eventBus) {
    throw new Error('Event bus service not available');
  }

  // Emit event
  const emit = async <T>(eventName: string, data: T): Promise<void> => {
    await eventBus.emit(eventName, data);
  };

  // Listen to event
  const on = <T>(eventName: string, handler: (data: T) => void | Promise<void>) => {
    const unsubscribe = eventBus.on(eventName, handler);

    // Auto-unsubscribe on unmount
    onUnmounted(unsubscribe);

    return unsubscribe;
  };

  // Listen once
  const once = <T>(eventName: string, handler: (data: T) => void | Promise<void>) => {
    const unsubscribe = eventBus.once(eventName, handler);

    // Auto-unsubscribe on unmount
    onUnmounted(unsubscribe);

    return unsubscribe;
  };

  // Reactive event listener
  const useEventListener = <T>(eventName: string, initialValue?: T) => {
    const data = ref<T | undefined>(initialValue);

    on<T>(eventName, (eventData) => {
      data.value = eventData;
    });

    return readonly(data);
  };

  return {
    emit,
    on,
    once,
    useEventListener,
  };
};
```

## Validation Composable

```typescript
// src/core/composables/useValidation.ts
export interface ValidationOptions {
  immediate?: boolean;
  debounce?: number;
}

export const useValidation = () => {
  // Field validation
  const useField = <T>(
    initialValue: T,
    rules: ValidationRule<T>[],
    options: ValidationOptions = {},
  ) => {
    const value = ref<T>(initialValue);
    const errors = ref<string[]>([]);
    const isValid = computed(() => errors.value.length === 0);
    const isDirty = ref(false);
    const isValidating = ref(false);

    const validate = async (): Promise<boolean> => {
      isValidating.value = true;
      errors.value = [];

      try {
        for (const rule of rules) {
          const isRuleValid = await rule.validate(value.value);
          if (!isRuleValid) {
            const message =
              typeof rule.message === 'function' ? rule.message(value.value) : rule.message;
            errors.value.push(message);
          }
        }
      } catch (error) {
        errors.value.push('Validation error occurred');
      } finally {
        isValidating.value = false;
      }

      return isValid.value;
    };

    // Debounced validation
    const debouncedValidate = options.debounce ? debounce(validate, options.debounce) : validate;

    // Watch for changes
    watch(value, () => {
      isDirty.value = true;
      if (options.immediate || isDirty.value) {
        debouncedValidate();
      }
    });

    // Validate immediately if requested
    if (options.immediate) {
      nextTick(validate);
    }

    return {
      value,
      errors: readonly(errors),
      isValid: readonly(isValid),
      isDirty: readonly(isDirty),
      isValidating: readonly(isValidating),
      validate,
    };
  };

  // Form validation
  const useForm = <T extends Record<string, any>>(initialValues: T, schema: ValidationSchema) => {
    const values = reactive<T>({ ...initialValues });
    const errors = reactive<Record<keyof T, string[]>>({} as Record<keyof T, string[]>);
    const isValid = computed(() =>
      Object.values(errors).every((fieldErrors) => fieldErrors.length === 0),
    );
    const isDirty = ref(false);
    const isValidating = ref(false);

    const validate = async (): Promise<boolean> => {
      isValidating.value = true;

      try {
        const result = await createSchemaValidator().validate(values, schema);

        // Clear previous errors
        Object.keys(errors).forEach((key) => {
          errors[key as keyof T] = [];
        });

        // Set new errors
        result.errors.forEach((error) => {
          if (!errors[error.field as keyof T]) {
            errors[error.field as keyof T] = [];
          }
          errors[error.field as keyof T].push(error.message);
        });

        return result.isValid;
      } finally {
        isValidating.value = false;
      }
    };

    const reset = () => {
      Object.assign(values, initialValues);
      Object.keys(errors).forEach((key) => {
        errors[key as keyof T] = [];
      });
      isDirty.value = false;
    };

    // Watch for changes
    watch(
      values,
      () => {
        isDirty.value = true;
      },
      { deep: true },
    );

    return {
      values,
      errors: readonly(errors),
      isValid: readonly(isValid),
      isDirty: readonly(isDirty),
      isValidating: readonly(isValidating),
      validate,
      reset,
    };
  };

  return {
    useField,
    useForm,
  };
};
```

## Authentication Composable

```typescript
// src/core/composables/useAuth.ts
export const useAuth = () => {
  const { api, cache, eventBus } = useCore();
  const { success, error } = useNotification();

  const user = ref<User | null>(null);
  const token = ref<string | null>(null);
  const isAuthenticated = computed(() => !!user.value && !!token.value);
  const isLoading = ref(false);

  // Login
  const login = async (credentials: LoginCredentials): Promise<Either<AppError, User>> => {
    isLoading.value = true;

    try {
      const result = await api.post<AuthResponse>('/auth/login', credentials);

      if (result.isRight()) {
        const authData = result.getOrElse({} as AuthResponse);
        user.value = authData.user;
        token.value = authData.token;

        // Cache auth data
        await cache?.set('auth.user', authData.user);
        await cache?.set('auth.token', authData.token);

        // Emit login event
        await eventBus?.emit('auth.login', { user: authData.user });

        success('Başarıyla giriş yapıldı');
        return Either.right(authData.user);
      } else {
        error('Giriş yapılamadı');
        return result;
      }
    } finally {
      isLoading.value = false;
    }
  };

  // Logout
  const logout = async (): Promise<void> => {
    isLoading.value = true;

    try {
      await api.post('/auth/logout');
    } catch (err) {
      // Continue with logout even if API call fails
    }

    user.value = null;
    token.value = null;

    // Clear cache
    await cache?.delete('auth.user');
    await cache?.delete('auth.token');

    // Emit logout event
    await eventBus?.emit('auth.logout', {});

    success('Başarıyla çıkış yapıldı');
    isLoading.value = false;
  };

  // Refresh token
  const refreshToken = async (): Promise<boolean> => {
    try {
      const result = await api.post<AuthResponse>('/auth/refresh');

      if (result.isRight()) {
        const authData = result.getOrElse({} as AuthResponse);
        user.value = authData.user;
        token.value = authData.token;

        await cache?.set('auth.user', authData.user);
        await cache?.set('auth.token', authData.token);

        return true;
      }
    } catch (err) {
      await logout();
    }

    return false;
  };

  // Initialize auth state
  const initialize = async (): Promise<void> => {
    isLoading.value = true;

    try {
      const cachedUser = await cache?.get<User>('auth.user');
      const cachedToken = await cache?.get<string>('auth.token');

      if (cachedUser && cachedToken) {
        user.value = cachedUser;
        token.value = cachedToken;

        // Verify token is still valid
        const isValid = await refreshToken();
        if (!isValid) {
          await logout();
        }
      }
    } finally {
      isLoading.value = false;
    }
  };

  // Auto-initialize on mount
  onMounted(initialize);

  return {
    user: readonly(user),
    token: readonly(token),
    isAuthenticated: readonly(isAuthenticated),
    isLoading: readonly(isLoading),
    login,
    logout,
    refreshToken,
    initialize,
  };
};
```

## Permission Composable

```typescript
// src/core/composables/usePermission.ts
export const usePermission = () => {
  const { user } = useAuth();
  const permissionService = inject<PermissionService>('permissionService');

  if (!permissionService) {
    throw new Error('Permission service not available');
  }

  // Check permission
  const hasPermission = (resource: string, action: string, context?: any): boolean => {
    if (!user.value) return false;
    return permissionService.hasPermission(user.value, resource, action, context);
  };

  // Check role
  const hasRole = (roleName: string): boolean => {
    if (!user.value) return false;
    return permissionService.hasRole(user.value, roleName);
  };

  // Reactive permission check
  const usePermissionCheck = (resource: string, action: string, context?: Ref<any>) => {
    return computed(() => {
      if (!user.value) return false;
      const ctx = context ? context.value : undefined;
      return permissionService.hasPermission(user.value, resource, action, ctx);
    });
  };

  // Reactive role check
  const useRoleCheck = (roleName: string) => {
    return computed(() => {
      if (!user.value) return false;
      return permissionService.hasRole(user.value, roleName);
    });
  };

  return {
    hasPermission,
    hasRole,
    usePermissionCheck,
    useRoleCheck,
  };
};
```
