import { defineStore } from 'pinia';
import { ref } from 'vue';

/**
 * Layout ile ilgili durumları ve eylemleri yöneten Pinia store'u.
 * Bu store, uygulamanın genel yerleşim (layout) durumunu merkezi bir yerden
 * yöneterek bileşenler arasındaki doğrudan bağımlılığı ortadan kaldırır.
 *
 * @see https://pinia.vuejs.org/
 */
export const useLayoutStore = defineStore('layout', () => {
    // --- STATE ---

    /**
     * Sol taraftaki çekmecenin (drawer) açık olup olmadığını belirten durum.
     * `ref` kullanılarak reaktif bir değişken oluşturulmuştur.
     */
    const isLeftDrawerOpen = ref(false);

    // --- ACTIONS ---

    /**
     * Sol çekmecenin görünürlüğünü tersine çevirir (a<PERSON><PERSON><PERSON><PERSON> ka<PERSON>, kapal<PERSON><PERSON><PERSON> a<PERSON>).
     * <PERSON>u fonksiyon, bileşenlerin durumu doğrudan değiştirmesi yer<PERSON>, merkezi bir
     * eylem aracılığıyla bu değişikliği yapmasını sağlayarak kodun daha öngörülebilir
     * ve yönetilebilir olmasını sağlar.
     */
    function toggleLeftDrawer(): void {
        isLeftDrawerOpen.value = !isLeftDrawerOpen.value;
    }

    return {
        // State
        isLeftDrawerOpen,
        // Actions
        toggleLeftDrawer,
    };
});