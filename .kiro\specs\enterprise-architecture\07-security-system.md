# Güvenlik Sistemi

## İzin Yönetimi (Permission System)

### Temel İzin Arayüzleri

```typescript
// src/core/services/security/permission.service.ts
export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: PermissionCondition[];
}

export interface PermissionCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
  value: any;
}

export interface Role {
  id: string;
  name: string;
  permissions: Permission[];
}

export interface User {
  id: string;
  roles: Role[];
  customPermissions?: Permission[];
}

export interface PermissionService {
  hasPermission(user: User, resource: string, action: string, context?: any): boolean;
  hasRole(user: User, roleName: string): boolean;
  getUserPermissions(user: User): Permission[];
  checkConditions(conditions: PermissionCondition[], context: any): boolean;
}
```

### İzin Servisi Implementasyonu

```typescript
const createPermissionService = (): PermissionService => {
  const checkConditions = (conditions: PermissionCondition[], context: any): boolean => {
    return conditions.every((condition) => {
      const value = context[condition.field];
      switch (condition.operator) {
        case 'equals':
          return value === condition.value;
        case 'not_equals':
          return value !== condition.value;
        case 'contains':
          return Array.isArray(value) && value.includes(condition.value);
        case 'not_contains':
          return Array.isArray(value) && !value.includes(condition.value);
        case 'greater_than':
          return value > condition.value;
        case 'less_than':
          return value < condition.value;
        default:
          return false;
      }
    });
  };

  return {
    hasPermission(user: User, resource: string, action: string, context?: any): boolean {
      const permissions = this.getUserPermissions(user);

      return permissions.some((permission) => {
        if (permission.resource !== resource || permission.action !== action) {
          return false;
        }

        if (permission.conditions && context) {
          return checkConditions(permission.conditions, context);
        }

        return true;
      });
    },

    hasRole(user: User, roleName: string): boolean {
      return user.roles.some((role) => role.name === roleName);
    },

    getUserPermissions(user: User): Permission[] {
      const rolePermissions = user.roles.flatMap((role) => role.permissions);
      const customPermissions = user.customPermissions || [];
      return [...rolePermissions, ...customPermissions];
    },

    checkConditions,
  };
};
```

## Kimlik Doğrulama (Authentication)

### Auth Service Arayüzü

```typescript
// src/core/services/security/auth.service.ts
export interface AuthCredentials {
  email: string;
  password: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  roles: string[];
  permissions: string[];
}

export interface AuthService {
  login(credentials: AuthCredentials): Promise<AuthTokens>;
  logout(): Promise<void>;
  refreshToken(): Promise<AuthTokens>;
  getCurrentUser(): Promise<AuthUser | null>;
  isAuthenticated(): boolean;
  getToken(): string | null;
  setToken(token: string): void;
  clearToken(): void;
}
```

### JWT Auth Service Implementasyonu

```typescript
// src/core/services/security/jwt-auth.service.ts
export class JWTAuthService implements AuthService {
  private currentUser: AuthUser | null = null;
  private tokenKey = 'auth_token';
  private refreshTokenKey = 'refresh_token';

  constructor(
    private apiService: ApiService,
    private storageService: StorageService,
    private eventBus: EventBusService,
  ) {
    this.initializeFromStorage();
  }

  async login(credentials: AuthCredentials): Promise<AuthTokens> {
    try {
      const response = await this.apiService.post<AuthTokens>('/auth/login', credentials);

      this.setToken(response.accessToken);
      this.storageService.setItem(this.refreshTokenKey, response.refreshToken);

      // Kullanıcı bilgilerini al
      this.currentUser = await this.fetchCurrentUser();

      // Login event'i yayınla
      await this.eventBus.emit('auth.login', { user: this.currentUser });

      return response;
    } catch (error) {
      throw new AuthenticationError('Login failed', { credentials: credentials.email });
    }
  }

  async logout(): Promise<void> {
    try {
      const refreshToken = this.storageService.getItem(this.refreshTokenKey);

      if (refreshToken) {
        await this.apiService.post('/auth/logout', { refreshToken });
      }
    } catch (error) {
      // Logout hatası kritik değil, devam et
      console.warn('Logout request failed:', error);
    } finally {
      this.clearToken();
      this.storageService.removeItem(this.refreshTokenKey);
      this.currentUser = null;

      // Logout event'i yayınla
      await this.eventBus.emit('auth.logout', {});
    }
  }

  async refreshToken(): Promise<AuthTokens> {
    const refreshToken = this.storageService.getItem(this.refreshTokenKey);

    if (!refreshToken) {
      throw new AuthenticationError('No refresh token available');
    }

    try {
      const response = await this.apiService.post<AuthTokens>('/auth/refresh', {
        refreshToken,
      });

      this.setToken(response.accessToken);
      this.storageService.setItem(this.refreshTokenKey, response.refreshToken);

      return response;
    } catch (error) {
      // Refresh başarısız, kullanıcıyı logout et
      await this.logout();
      throw new AuthenticationError('Token refresh failed');
    }
  }

  async getCurrentUser(): Promise<AuthUser | null> {
    if (!this.isAuthenticated()) {
      return null;
    }

    if (!this.currentUser) {
      this.currentUser = await this.fetchCurrentUser();
    }

    return this.currentUser;
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;

    try {
      const payload = this.decodeJWT(token);
      return payload.exp * 1000 > Date.now();
    } catch {
      return false;
    }
  }

  getToken(): string | null {
    return this.storageService.getItem(this.tokenKey);
  }

  setToken(token: string): void {
    this.storageService.setItem(this.tokenKey, token);
  }

  clearToken(): void {
    this.storageService.removeItem(this.tokenKey);
  }

  private async fetchCurrentUser(): Promise<AuthUser> {
    return await this.apiService.get<AuthUser>('/auth/me');
  }

  private initializeFromStorage(): void {
    if (this.isAuthenticated()) {
      // Kullanıcı bilgilerini background'da al
      this.fetchCurrentUser()
        .then((user) => {
          this.currentUser = user;
        })
        .catch(() => {
          // Hata durumunda logout et
          this.logout();
        });
    }
  }

  private decodeJWT(token: string): any {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join(''),
    );
    return JSON.parse(jsonPayload);
  }
}
```

## Şifreleme Servisi

### Encryption Service

```typescript
// src/core/services/security/encryption.service.ts
export interface EncryptionService {
  encrypt(data: string, key?: string): Promise<string>;
  decrypt(encryptedData: string, key?: string): Promise<string>;
  hash(data: string, salt?: string): Promise<string>;
  compareHash(data: string, hash: string): Promise<boolean>;
  generateSalt(): string;
  generateKey(): string;
}

export class CryptoEncryptionService implements EncryptionService {
  private defaultKey: string;

  constructor(config: SecurityConfig) {
    this.defaultKey = config.encryption.algorithm;
  }

  async encrypt(data: string, key?: string): Promise<string> {
    const encoder = new TextEncoder();
    const keyToUse = key || this.defaultKey;

    // Generate IV
    const iv = crypto.getRandomValues(new Uint8Array(12));

    // Import key
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      encoder.encode(keyToUse),
      { name: 'AES-GCM' },
      false,
      ['encrypt'],
    );

    // Encrypt
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      cryptoKey,
      encoder.encode(data),
    );

    // Combine IV and encrypted data
    const combined = new Uint8Array(iv.length + encrypted.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(encrypted), iv.length);

    return btoa(String.fromCharCode(...combined));
  }

  async decrypt(encryptedData: string, key?: string): Promise<string> {
    const decoder = new TextDecoder();
    const keyToUse = key || this.defaultKey;

    // Decode base64
    const combined = new Uint8Array(
      atob(encryptedData)
        .split('')
        .map((char) => char.charCodeAt(0)),
    );

    // Extract IV and encrypted data
    const iv = combined.slice(0, 12);
    const encrypted = combined.slice(12);

    // Import key
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(keyToUse),
      { name: 'AES-GCM' },
      false,
      ['decrypt'],
    );

    // Decrypt
    const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, cryptoKey, encrypted);

    return decoder.decode(decrypted);
  }

  async hash(data: string, salt?: string): Promise<string> {
    const encoder = new TextEncoder();
    const saltToUse = salt || this.generateSalt();
    const combined = data + saltToUse;

    const hashBuffer = await crypto.subtle.digest('SHA-256', encoder.encode(combined));
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');

    return `${hashHex}:${saltToUse}`;
  }

  async compareHash(data: string, hash: string): Promise<boolean> {
    const [hashValue, salt] = hash.split(':');
    const newHash = await this.hash(data, salt);
    const [newHashValue] = newHash.split(':');

    return hashValue === newHashValue;
  }

  generateSalt(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('');
  }

  generateKey(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('');
  }
}
```

## Route Guards

### Authentication Guard

```typescript
// src/router/guards/auth.guard.ts
export const authGuard = (authService: AuthService) => {
  return async (to: RouteLocationNormalized, from: RouteLocationNormalized) => {
    const isAuthenticated = authService.isAuthenticated();

    if (!isAuthenticated) {
      return {
        name: 'login',
        query: { redirect: to.fullPath },
      };
    }

    return true;
  };
};
```

### Permission Guard

```typescript
// src/router/guards/permission.guard.ts
export const permissionGuard = (authService: AuthService, permissionService: PermissionService) => {
  return async (to: RouteLocationNormalized) => {
    const user = await authService.getCurrentUser();

    if (!user) {
      return { name: 'login' };
    }

    const requiredPermission = to.meta.permission as {
      resource: string;
      action: string;
    };

    if (requiredPermission) {
      const hasPermission = permissionService.hasPermission(
        user,
        requiredPermission.resource,
        requiredPermission.action,
      );

      if (!hasPermission) {
        return { name: 'forbidden' };
      }
    }

    return true;
  };
};
```

### Role Guard

```typescript
// src/router/guards/role.guard.ts
export const roleGuard = (authService: AuthService, permissionService: PermissionService) => {
  return async (to: RouteLocationNormalized) => {
    const user = await authService.getCurrentUser();

    if (!user) {
      return { name: 'login' };
    }

    const requiredRoles = to.meta.roles as string[];

    if (requiredRoles && requiredRoles.length > 0) {
      const hasRole = requiredRoles.some((role) => permissionService.hasRole(user, role));

      if (!hasRole) {
        return { name: 'forbidden' };
      }
    }

    return true;
  };
};
```

## Security Composables

### Auth Composable

```typescript
// src/core/composables/useAuth.ts
export const useAuth = () => {
  const authService = inject<AuthService>('authService');
  const permissionService = inject<PermissionService>('permissionService');

  if (!authService || !permissionService) {
    throw new Error('Auth services not found');
  }

  const user = ref<AuthUser | null>(null);
  const isAuthenticated = ref(false);
  const loading = ref(false);

  const login = async (credentials: AuthCredentials) => {
    loading.value = true;
    try {
      await authService.login(credentials);
      await refreshUser();
    } finally {
      loading.value = false;
    }
  };

  const logout = async () => {
    loading.value = true;
    try {
      await authService.logout();
      user.value = null;
      isAuthenticated.value = false;
    } finally {
      loading.value = false;
    }
  };

  const refreshUser = async () => {
    const currentUser = await authService.getCurrentUser();
    user.value = currentUser;
    isAuthenticated.value = !!currentUser;
  };

  const hasPermission = (resource: string, action: string, context?: any) => {
    if (!user.value) return false;
    return permissionService.hasPermission(user.value, resource, action, context);
  };

  const hasRole = (roleName: string) => {
    if (!user.value) return false;
    return permissionService.hasRole(user.value, roleName);
  };

  const hasAnyRole = (roleNames: string[]) => {
    return roleNames.some((role) => hasRole(role));
  };

  // Initialize
  onMounted(async () => {
    if (authService.isAuthenticated()) {
      await refreshUser();
    }
  });

  return {
    user: readonly(user),
    isAuthenticated: readonly(isAuthenticated),
    loading: readonly(loading),
    login,
    logout,
    refreshUser,
    hasPermission,
    hasRole,
    hasAnyRole,
  };
};
```

### Permission Composable

```typescript
// src/core/composables/usePermission.ts
export const usePermission = () => {
  const { user, hasPermission, hasRole } = useAuth();

  const can = (resource: string, action: string, context?: any) => {
    return computed(() => hasPermission(resource, action, context));
  };

  const cannot = (resource: string, action: string, context?: any) => {
    return computed(() => !hasPermission(resource, action, context));
  };

  const is = (roleName: string) => {
    return computed(() => hasRole(roleName));
  };

  const isNot = (roleName: string) => {
    return computed(() => !hasRole(roleName));
  };

  return {
    user,
    can,
    cannot,
    is,
    isNot,
  };
};
```

## Security Directives

### Permission Directive

```typescript
// src/core/directives/permission.directive.ts
export const vPermission = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { hasPermission } = usePermission();
    const [resource, action] = binding.value.split(':');

    if (!hasPermission(resource, action)) {
      el.style.display = 'none';
    }
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    const { hasPermission } = usePermission();
    const [resource, action] = binding.value.split(':');

    if (hasPermission(resource, action)) {
      el.style.display = '';
    } else {
      el.style.display = 'none';
    }
  },
};

// Kullanım:
// <button v-permission="'users:delete'">Delete User</button>
```

### Role Directive

```typescript
// src/core/directives/role.directive.ts
export const vRole = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { hasRole } = useAuth();
    const roles = Array.isArray(binding.value) ? binding.value : [binding.value];

    const hasAnyRole = roles.some((role) => hasRole(role));

    if (!hasAnyRole) {
      el.style.display = 'none';
    }
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    const { hasRole } = useAuth();
    const roles = Array.isArray(binding.value) ? binding.value : [binding.value];

    const hasAnyRole = roles.some((role) => hasRole(role));

    if (hasAnyRole) {
      el.style.display = '';
    } else {
      el.style.display = 'none';
    }
  },
};

// Kullanım:
// <div v-role="'admin'">Admin Panel</div>
// <div v-role="['admin', 'moderator']">Admin or Moderator Panel</div>
```

## Security Middleware

### API Request Interceptor

```typescript
// src/core/services/api/interceptors/auth.interceptor.ts
export const createAuthInterceptor = (authService: AuthService) => {
  return {
    request: async (config: any) => {
      const token = authService.getToken();

      if (token) {
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${token}`,
        };
      }

      return config;
    },

    response: async (response: any) => {
      return response;
    },

    error: async (error: any) => {
      if (error.response?.status === 401) {
        try {
          // Token yenilemeyi dene
          await authService.refreshToken();

          // Orijinal isteği yeniden dene
          const originalRequest = error.config;
          const newToken = authService.getToken();

          if (newToken) {
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return fetch(originalRequest.url, originalRequest);
          }
        } catch {
          // Refresh başarısız, logout et
          await authService.logout();
          window.location.href = '/login';
        }
      }

      throw error;
    },
  };
};
```

Bu güvenlik sistemi, modern web uygulamaları için gerekli tüm güvenlik özelliklerini sağlar: kimlik doğrulama, yetkilendirme, şifreleme, route koruması ve güvenlik middleware'leri. Sistem ayrıca Vue.js ile entegre olacak şekilde composable'lar ve directive'ler de sağlar.
