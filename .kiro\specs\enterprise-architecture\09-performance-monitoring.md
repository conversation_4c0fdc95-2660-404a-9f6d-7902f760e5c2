# Performans İzleme Sistemi

## Temel Performans Arayüzleri

### Performance Metric Interface

```typescript
// src/core/utils/performance.utils.ts
export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
  unit?: 'ms' | 'bytes' | 'count' | 'percentage';
}

export interface PerformanceMonitor {
  startTimer(name: string, tags?: Record<string, string>): () => void;
  recordMetric(metric: PerformanceMetric): void;
  getMetrics(name?: string): PerformanceMetric[];
  getAverageMetric(name: string, timeWindow?: number): number | null;
  clearMetrics(): void;
  exportMetrics(): PerformanceReport;
}

export interface PerformanceReport {
  summary: {
    totalMetrics: number;
    timeRange: { start: number; end: number };
    categories: string[];
  };
  metrics: PerformanceMetric[];
  aggregations: {
    [metricName: string]: {
      count: number;
      average: number;
      min: number;
      max: number;
      percentiles: {
        p50: number;
        p90: number;
        p95: number;
        p99: number;
      };
    };
  };
}
```

## Performans İzleme Servisi Implementasyonu

```typescript
// src/core/services/performance/performance-monitor.service.ts
const createPerformanceMonitor = (): PerformanceMonitor => {
  const metrics: PerformanceMetric[] = [];
  const timers = new Map<string, { startTime: number; tags?: Record<string, string> }>();

  return {
    startTimer(name: string, tags?: Record<string, string>): () => void {
      const startTime = performance.now();
      const timerId = `${name}_${Date.now()}_${Math.random()}`;

      timers.set(timerId, { startTime, tags });

      return () => {
        const timerData = timers.get(timerId);
        if (!timerData) return;

        const endTime = performance.now();
        const duration = endTime - timerData.startTime;

        this.recordMetric({
          name,
          value: duration,
          timestamp: Date.now(),
          tags: { ...timerData.tags, type: 'timer' },
          unit: 'ms',
        });

        timers.delete(timerId);
      };
    },

    recordMetric(metric: PerformanceMetric): void {
      metrics.push({
        ...metric,
        timestamp: metric.timestamp || Date.now(),
      });

      // Sadece son 10000 metriği tut (bellek yönetimi)
      if (metrics.length > 10000) {
        metrics.splice(0, metrics.length - 10000);
      }
    },

    getMetrics(name?: string): PerformanceMetric[] {
      return name ? metrics.filter((m) => m.name === name) : [...metrics];
    },

    getAverageMetric(name: string, timeWindow?: number): number | null {
      const now = Date.now();
      const windowStart = timeWindow ? now - timeWindow : 0;

      const relevantMetrics = metrics.filter((m) => m.name === name && m.timestamp >= windowStart);

      if (relevantMetrics.length === 0) return null;

      const sum = relevantMetrics.reduce((acc, m) => acc + m.value, 0);
      return sum / relevantMetrics.length;
    },

    clearMetrics(): void {
      metrics.length = 0;
      timers.clear();
    },

    exportMetrics(): PerformanceReport {
      const now = Date.now();
      const metricsByName = new Map<string, PerformanceMetric[]>();

      // Metrikleri isme göre grupla
      metrics.forEach((metric) => {
        if (!metricsByName.has(metric.name)) {
          metricsByName.set(metric.name, []);
        }
        metricsByName.get(metric.name)!.push(metric);
      });

      // Aggregasyonları hesapla
      const aggregations: PerformanceReport['aggregations'] = {};

      metricsByName.forEach((metricList, name) => {
        const values = metricList.map((m) => m.value).sort((a, b) => a - b);
        const count = values.length;

        if (count > 0) {
          const sum = values.reduce((acc, val) => acc + val, 0);
          const average = sum / count;
          const min = values[0];
          const max = values[count - 1];

          // Percentile hesaplamaları
          const getPercentile = (p: number) => {
            const index = Math.ceil((p / 100) * count) - 1;
            return values[Math.max(0, index)];
          };

          aggregations[name] = {
            count,
            average,
            min,
            max,
            percentiles: {
              p50: getPercentile(50),
              p90: getPercentile(90),
              p95: getPercentile(95),
              p99: getPercentile(99),
            },
          };
        }
      });

      return {
        summary: {
          totalMetrics: metrics.length,
          timeRange: {
            start: metrics.length > 0 ? Math.min(...metrics.map((m) => m.timestamp)) : now,
            end: metrics.length > 0 ? Math.max(...metrics.map((m) => m.timestamp)) : now,
          },
          categories: Array.from(metricsByName.keys()),
        },
        metrics: [...metrics],
        aggregations,
      };
    },
  };
};
```

## Web Vitals İzleme

```typescript
// src/core/services/performance/web-vitals.service.ts
export interface WebVitalsMetric {
  name: 'CLS' | 'FID' | 'FCP' | 'LCP' | 'TTFB';
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
}

export interface WebVitalsService {
  startMonitoring(): void;
  stopMonitoring(): void;
  getMetrics(): WebVitalsMetric[];
  onMetric(callback: (metric: WebVitalsMetric) => void): () => void;
}

const createWebVitalsService = (performanceMonitor: PerformanceMonitor): WebVitalsService => {
  const callbacks: Array<(metric: WebVitalsMetric) => void> = [];
  const metrics: WebVitalsMetric[] = [];
  let isMonitoring = false;

  const reportMetric = (metric: WebVitalsMetric) => {
    metrics.push(metric);

    // Performance monitor'a kaydet
    performanceMonitor.recordMetric({
      name: `web-vitals-${metric.name.toLowerCase()}`,
      value: metric.value,
      timestamp: Date.now(),
      tags: {
        rating: metric.rating,
        type: 'web-vitals',
      },
      unit: metric.name === 'CLS' ? 'count' : 'ms',
    });

    // Callback'leri çağır
    callbacks.forEach((callback) => callback(metric));
  };

  const initWebVitals = async () => {
    try {
      // Web Vitals kütüphanesini dinamik olarak yükle
      const { getCLS, getFID, getFCP, getLCP, getTTFB } = await import('web-vitals');

      getCLS(reportMetric);
      getFID(reportMetric);
      getFCP(reportMetric);
      getLCP(reportMetric);
      getTTFB(reportMetric);
    } catch (error) {
      console.warn('Web Vitals could not be loaded:', error);
    }
  };

  return {
    startMonitoring(): void {
      if (isMonitoring) return;

      isMonitoring = true;
      initWebVitals();
    },

    stopMonitoring(): void {
      isMonitoring = false;
      // Web Vitals observer'ları durdurulamaz, sadece flag'i değiştiriyoruz
    },

    getMetrics(): WebVitalsMetric[] {
      return [...metrics];
    },

    onMetric(callback: (metric: WebVitalsMetric) => void): () => void {
      callbacks.push(callback);

      return () => {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      };
    },
  };
};
```

## Resource Monitoring

```typescript
// src/core/services/performance/resource-monitor.service.ts
export interface ResourceMetric {
  name: string;
  type: 'navigation' | 'resource' | 'paint' | 'measure' | 'mark';
  startTime: number;
  duration: number;
  transferSize?: number;
  encodedBodySize?: number;
  decodedBodySize?: number;
}

export interface ResourceMonitorService {
  startMonitoring(): void;
  stopMonitoring(): void;
  getResourceMetrics(): ResourceMetric[];
  getNavigationMetrics(): ResourceMetric[];
}

const createResourceMonitorService = (
  performanceMonitor: PerformanceMonitor,
): ResourceMonitorService => {
  let observer: PerformanceObserver | null = null;
  const resourceMetrics: ResourceMetric[] = [];
  const navigationMetrics: ResourceMetric[] = [];

  const processEntry = (entry: PerformanceEntry) => {
    const metric: ResourceMetric = {
      name: entry.name,
      type: entry.entryType as ResourceMetric['type'],
      startTime: entry.startTime,
      duration: entry.duration,
    };

    // Resource entry için ek bilgiler
    if (entry.entryType === 'resource') {
      const resourceEntry = entry as PerformanceResourceTiming;
      metric.transferSize = resourceEntry.transferSize;
      metric.encodedBodySize = resourceEntry.encodedBodySize;
      metric.decodedBodySize = resourceEntry.decodedBodySize;

      resourceMetrics.push(metric);
    } else if (entry.entryType === 'navigation') {
      navigationMetrics.push(metric);
    }

    // Performance monitor'a kaydet
    performanceMonitor.recordMetric({
      name: `resource-${entry.entryType}`,
      value: entry.duration,
      timestamp: Date.now(),
      tags: {
        resourceName: entry.name,
        resourceType: entry.entryType,
      },
      unit: 'ms',
    });
  };

  return {
    startMonitoring(): void {
      if (observer) return;

      try {
        observer = new PerformanceObserver((list) => {
          list.getEntries().forEach(processEntry);
        });

        observer.observe({
          entryTypes: ['resource', 'navigation', 'paint', 'measure', 'mark'],
        });
      } catch (error) {
        console.warn('Resource monitoring could not be started:', error);
      }
    },

    stopMonitoring(): void {
      if (observer) {
        observer.disconnect();
        observer = null;
      }
    },

    getResourceMetrics(): ResourceMetric[] {
      return [...resourceMetrics];
    },

    getNavigationMetrics(): ResourceMetric[] {
      return [...navigationMetrics];
    },
  };
};
```

## Memory Monitoring

```typescript
// src/core/services/performance/memory-monitor.service.ts
export interface MemoryMetric {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  timestamp: number;
}

export interface MemoryMonitorService {
  startMonitoring(interval?: number): void;
  stopMonitoring(): void;
  getCurrentMemoryUsage(): MemoryMetric | null;
  getMemoryHistory(): MemoryMetric[];
  detectMemoryLeaks(): boolean;
}

const createMemoryMonitorService = (
  performanceMonitor: PerformanceMonitor,
): MemoryMonitorService => {
  let intervalId: NodeJS.Timeout | null = null;
  const memoryHistory: MemoryMetric[] = [];
  const maxHistorySize = 1000;

  const recordMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const metric: MemoryMetric = {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        timestamp: Date.now(),
      };

      memoryHistory.push(metric);

      // Geçmiş boyutunu sınırla
      if (memoryHistory.length > maxHistorySize) {
        memoryHistory.splice(0, memoryHistory.length - maxHistorySize);
      }

      // Performance monitor'a kaydet
      performanceMonitor.recordMetric({
        name: 'memory-used-heap',
        value: metric.usedJSHeapSize,
        timestamp: metric.timestamp,
        tags: { type: 'memory' },
        unit: 'bytes',
      });

      performanceMonitor.recordMetric({
        name: 'memory-total-heap',
        value: metric.totalJSHeapSize,
        timestamp: metric.timestamp,
        tags: { type: 'memory' },
        unit: 'bytes',
      });
    }
  };

  return {
    startMonitoring(interval: number = 5000): void {
      if (intervalId) return;

      recordMemoryUsage(); // İlk ölçümü hemen al
      intervalId = setInterval(recordMemoryUsage, interval);
    },

    stopMonitoring(): void {
      if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
      }
    },

    getCurrentMemoryUsage(): MemoryMetric | null {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        return {
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
          timestamp: Date.now(),
        };
      }
      return null;
    },

    getMemoryHistory(): MemoryMetric[] {
      return [...memoryHistory];
    },

    detectMemoryLeaks(): boolean {
      if (memoryHistory.length < 10) return false;

      // Son 10 ölçümün ortalamasını al
      const recent = memoryHistory.slice(-10);
      const recentAvg = recent.reduce((sum, m) => sum + m.usedJSHeapSize, 0) / recent.length;

      // İlk 10 ölçümün ortalamasını al
      const initial = memoryHistory.slice(0, 10);
      const initialAvg = initial.reduce((sum, m) => sum + m.usedJSHeapSize, 0) / initial.length;

      // %50'den fazla artış varsa memory leak olabilir
      return (recentAvg - initialAvg) / initialAvg > 0.5;
    },
  };
};
```

## Performance Composable

```typescript
// src/core/composables/usePerformance.ts
export const usePerformance = () => {
  const performanceMonitor = inject<PerformanceMonitor>('performance');
  const webVitals = inject<WebVitalsService>('webVitals');
  const resourceMonitor = inject<ResourceMonitorService>('resourceMonitor');
  const memoryMonitor = inject<MemoryMonitorService>('memoryMonitor');

  if (!performanceMonitor) {
    throw new Error('Performance monitor not available');
  }

  // Component lifecycle tracking
  const trackComponentLifecycle = (componentName: string) => {
    const mountTimer = performanceMonitor.startTimer(`component-mount-${componentName}`, {
      component: componentName,
      lifecycle: 'mount',
    });

    onMounted(() => {
      mountTimer();
    });

    onBeforeUnmount(() => {
      const unmountTimer = performanceMonitor.startTimer(`component-unmount-${componentName}`, {
        component: componentName,
        lifecycle: 'unmount',
      });

      nextTick(() => {
        unmountTimer();
      });
    });
  };

  // API call tracking
  const trackApiCall = (endpoint: string) => {
    return performanceMonitor.startTimer(`api-call`, {
      endpoint,
      type: 'api',
    });
  };

  // Route change tracking
  const trackRouteChange = (from: string, to: string) => {
    return performanceMonitor.startTimer(`route-change`, {
      from,
      to,
      type: 'navigation',
    });
  };

  // Custom metric recording
  const recordCustomMetric = (name: string, value: number, tags?: Record<string, string>) => {
    performanceMonitor.recordMetric({
      name,
      value,
      timestamp: Date.now(),
      tags: { ...tags, type: 'custom' },
    });
  };

  return {
    // Core performance monitor
    startTimer: performanceMonitor.startTimer.bind(performanceMonitor),
    recordMetric: performanceMonitor.recordMetric.bind(performanceMonitor),
    getMetrics: performanceMonitor.getMetrics.bind(performanceMonitor),
    getAverageMetric: performanceMonitor.getAverageMetric.bind(performanceMonitor),
    exportMetrics: performanceMonitor.exportMetrics.bind(performanceMonitor),

    // Specialized tracking
    trackComponentLifecycle,
    trackApiCall,
    trackRouteChange,
    recordCustomMetric,

    // Web Vitals (if available)
    webVitals: webVitals
      ? {
          getMetrics: webVitals.getMetrics.bind(webVitals),
          onMetric: webVitals.onMetric.bind(webVitals),
        }
      : null,

    // Resource monitoring (if available)
    resourceMonitor: resourceMonitor
      ? {
          getResourceMetrics: resourceMonitor.getResourceMetrics.bind(resourceMonitor),
          getNavigationMetrics: resourceMonitor.getNavigationMetrics.bind(resourceMonitor),
        }
      : null,

    // Memory monitoring (if available)
    memoryMonitor: memoryMonitor
      ? {
          getCurrentMemoryUsage: memoryMonitor.getCurrentMemoryUsage.bind(memoryMonitor),
          getMemoryHistory: memoryMonitor.getMemoryHistory.bind(memoryMonitor),
          detectMemoryLeaks: memoryMonitor.detectMemoryLeaks.bind(memoryMonitor),
        }
      : null,
  };
};
```

## Performance Dashboard Component

```vue
<!-- src/shared/components/ui/PerformanceDashboard.vue -->
<template>
  <div class="performance-dashboard">
    <q-card class="q-ma-md">
      <q-card-section>
        <div class="text-h6">Performans İzleme Dashboard'u</div>
      </q-card-section>

      <q-card-section>
        <q-tabs v-model="activeTab" class="text-grey" active-color="primary">
          <q-tab name="overview" label="Genel Bakış" />
          <q-tab name="web-vitals" label="Web Vitals" />
          <q-tab name="resources" label="Kaynaklar" />
          <q-tab name="memory" label="Bellek" />
        </q-tabs>

        <q-tab-panels v-model="activeTab" animated>
          <!-- Genel Bakış -->
          <q-tab-panel name="overview">
            <div class="row q-gutter-md">
              <div
                class="col-md-3 col-sm-6 col-xs-12"
                v-for="metric in overviewMetrics"
                :key="metric.name"
              >
                <q-card class="metric-card">
                  <q-card-section>
                    <div class="text-h4 text-primary">{{ metric.value }}</div>
                    <div class="text-subtitle2">{{ metric.name }}</div>
                    <div class="text-caption text-grey">{{ metric.unit }}</div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </q-tab-panel>

          <!-- Web Vitals -->
          <q-tab-panel name="web-vitals">
            <div class="row q-gutter-md">
              <div
                class="col-md-4 col-sm-6 col-xs-12"
                v-for="vital in webVitalsMetrics"
                :key="vital.name"
              >
                <q-card class="vital-card">
                  <q-card-section>
                    <div class="row items-center">
                      <div class="col">
                        <div class="text-h5">{{ vital.value.toFixed(2) }}</div>
                        <div class="text-subtitle2">{{ vital.name }}</div>
                      </div>
                      <div class="col-auto">
                        <q-badge :color="getVitalColor(vital.rating)" :label="vital.rating" />
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </q-tab-panel>

          <!-- Kaynaklar -->
          <q-tab-panel name="resources">
            <q-table
              :rows="resourceMetrics"
              :columns="resourceColumns"
              row-key="name"
              :pagination="{ rowsPerPage: 10 }"
            />
          </q-tab-panel>

          <!-- Bellek -->
          <q-tab-panel name="memory">
            <div class="row q-gutter-md">
              <div class="col-12">
                <q-card>
                  <q-card-section>
                    <div class="text-h6">Bellek Kullanımı</div>
                    <div v-if="currentMemory">
                      <div class="text-body1">
                        Kullanılan: {{ formatBytes(currentMemory.usedJSHeapSize) }}
                      </div>
                      <div class="text-body1">
                        Toplam: {{ formatBytes(currentMemory.totalJSHeapSize) }}
                      </div>
                      <div class="text-body1">
                        Limit: {{ formatBytes(currentMemory.jsHeapSizeLimit) }}
                      </div>
                    </div>
                    <div v-if="memoryLeakDetected" class="text-negative q-mt-md">
                      ⚠️ Potansiyel bellek sızıntısı tespit edildi!
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </q-tab-panel>
        </q-tab-panels>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { usePerformance } from '@/core/composables/usePerformance';

const { getMetrics, exportMetrics, webVitals, resourceMonitor, memoryMonitor } = usePerformance();

const activeTab = ref('overview');
const refreshInterval = ref<NodeJS.Timeout | null>(null);

// Reactive data
const performanceReport = ref(exportMetrics());
const webVitalsMetrics = ref<any[]>([]);
const resourceMetrics = ref<any[]>([]);
const currentMemory = ref<any>(null);
const memoryLeakDetected = ref(false);

// Computed properties
const overviewMetrics = computed(() => {
  const report = performanceReport.value;
  return [
    {
      name: 'Toplam Metrik',
      value: report.summary.totalMetrics,
      unit: 'adet',
    },
    {
      name: 'Aktif Kategori',
      value: report.summary.categories.length,
      unit: 'adet',
    },
    {
      name: 'Ortalama API Süresi',
      value: getAverageForCategory('api-call')?.toFixed(2) || '0',
      unit: 'ms',
    },
    {
      name: 'Ortalama Sayfa Yükleme',
      value: getAverageForCategory('route-change')?.toFixed(2) || '0',
      unit: 'ms',
    },
  ];
});

// Resource table columns
const resourceColumns = [
  {
    name: 'name',
    label: 'Kaynak Adı',
    field: 'name',
    align: 'left',
  },
  {
    name: 'type',
    label: 'Tür',
    field: 'type',
    align: 'left',
  },
  {
    name: 'duration',
    label: 'Süre (ms)',
    field: 'duration',
    format: (val: number) => val.toFixed(2),
  },
  {
    name: 'size',
    label: 'Boyut',
    field: 'transferSize',
    format: (val: number) => (val ? formatBytes(val) : '-'),
  },
];

// Methods
const getAverageForCategory = (category: string) => {
  const report = performanceReport.value;
  return report.aggregations[category]?.average;
};

const getVitalColor = (rating: string) => {
  switch (rating) {
    case 'good':
      return 'positive';
    case 'needs-improvement':
      return 'warning';
    case 'poor':
      return 'negative';
    default:
      return 'grey';
  }
};

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const refreshData = () => {
  // Performance report'u güncelle
  performanceReport.value = exportMetrics();

  // Web Vitals güncelle
  if (webVitals) {
    webVitalsMetrics.value = webVitals.getMetrics();
  }

  // Resource metrics güncelle
  if (resourceMonitor) {
    resourceMetrics.value = resourceMonitor.getResourceMetrics();
  }

  // Memory metrics güncelle
  if (memoryMonitor) {
    currentMemory.value = memoryMonitor.getCurrentMemoryUsage();
    memoryLeakDetected.value = memoryMonitor.detectMemoryLeaks();
  }
};

// Lifecycle
onMounted(() => {
  refreshData();

  // Her 5 saniyede bir güncelle
  refreshInterval.value = setInterval(refreshData, 5000);
});

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
  }
});
</script>

<style scoped lang="scss">
.performance-dashboard {
  .metric-card {
    text-align: center;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .vital-card {
    min-height: 100px;
  }
}
</style>
```

## Boot File Integration

```typescript
// src/boot/performance.boot.ts
import { boot } from 'quasar/wrappers';
import { createPerformanceMonitor } from '@/core/services/performance/performance-monitor.service';
import { createWebVitalsService } from '@/core/services/performance/web-vitals.service';
import { createResourceMonitorService } from '@/core/services/performance/resource-monitor.service';
import { createMemoryMonitorService } from '@/core/services/performance/memory-monitor.service';

export default boot(({ app }) => {
  // Performance monitor'u oluştur
  const performanceMonitor = createPerformanceMonitor();

  // Diğer servisleri oluştur
  const webVitals = createWebVitalsService(performanceMonitor);
  const resourceMonitor = createResourceMonitorService(performanceMonitor);
  const memoryMonitor = createMemoryMonitorService(performanceMonitor);

  // Servisleri provide et
  app.provide('performance', performanceMonitor);
  app.provide('webVitals', webVitals);
  app.provide('resourceMonitor', resourceMonitor);
  app.provide('memoryMonitor', memoryMonitor);

  // Monitoring'i başlat (production'da)
  if (process.env.NODE_ENV === 'production') {
    webVitals.startMonitoring();
    resourceMonitor.startMonitoring();
    memoryMonitor.startMonitoring();
  }
});
```

```

```
