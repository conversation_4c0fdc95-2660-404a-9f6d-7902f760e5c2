# <PERSON><PERSON><PERSON> ve En İyi Pratikler

## SOLID Prensipleri Uygulaması

### Single Responsibility Principle (SRP)

Her sınıf ve fonksiyon sadece tek bir sorumluluğa sahip olmalıdır.

```typescript
// ✅ Doğru yaklaşım - Her servis tek sorumluluğa sahip
export class AuthService {
  // Sadece kimlik doğrulama işlemleri
  async login(credentials: LoginCredentials): Promise<AuthResponse> {}
  async logout(): Promise<void> {}
  async refreshToken(): Promise<AuthResponse> {}
}

export class UserService {
  // Sadece kullanıcı yönetimi işlemleri
  async getUser(id: string): Promise<User> {}
  async updateUser(user: User): Promise<User> {}
  async deleteUser(id: string): Promise<void> {}
}

export class NotificationService {
  // Sadece bildirim işlemleri
  show(message: string, type: NotificationType): void {}
  dismiss(id: string): void {}
}

// ❌ Yanlış yaklaşım - Çok fazla sorumluluk
export class UserAuthNotificationService {
  // Çok fazla sorumluluk - SRP ihlali
  async login() {}
  async getUser() {}
  showNotification() {}
}
```

### Open/Closed Principle (OCP)

Sınıflar genişletmeye açık, değişikliğe kapalı olmalıdır.

```typescript
// ✅ Doğru yaklaşım - Factory pattern ile genişletilebilir
export interface ApiService {
  get<T>(url: string): Promise<T>;
  post<T>(url: string, data: any): Promise<T>;
}

export class RestApiService implements ApiService {
  async get<T>(url: string): Promise<T> {
    // REST API implementasyonu
  }

  async post<T>(url: string, data: any): Promise<T> {
    // REST API implementasyonu
  }
}

export class GraphQLApiService implements ApiService {
  async get<T>(url: string): Promise<T> {
    // GraphQL implementasyonu
  }

  async post<T>(url: string, data: any): Promise<T> {
    // GraphQL implementasyonu
  }
}

// Factory ile yeni implementasyonlar eklenebilir
export const createApiService = (type: 'rest' | 'graphql'): ApiService => {
  switch (type) {
    case 'rest':
      return new RestApiService();
    case 'graphql':
      return new GraphQLApiService();
    default:
      throw new Error('Unsupported API type');
  }
};
```

### Liskov Substitution Principle (LSP)

Alt sınıflar, üst sınıfların yerine geçebilmelidir.

```typescript
// ✅ Doğru yaklaşım - Tüm implementasyonlar interface'i tam karşılar
export interface Logger {
  info(message: string, data?: any): void;
  error(message: string, error?: any): void;
  warn(message: string, data?: any): void;
  debug(message: string, data?: any): void;
}

export class ConsoleLogger implements Logger {
  info(message: string, data?: any): void {
    console.log(`[INFO] ${message}`, data);
  }

  error(message: string, error?: any): void {
    console.error(`[ERROR] ${message}`, error);
  }

  warn(message: string, data?: any): void {
    console.warn(`[WARN] ${message}`, data);
  }

  debug(message: string, data?: any): void {
    console.debug(`[DEBUG] ${message}`, data);
  }
}

export class RemoteLogger implements Logger {
  info(message: string, data?: any): void {
    this.sendToRemote('info', message, data);
  }

  error(message: string, error?: any): void {
    this.sendToRemote('error', message, error);
  }

  warn(message: string, data?: any): void {
    this.sendToRemote('warn', message, data);
  }

  debug(message: string, data?: any): void {
    this.sendToRemote('debug', message, data);
  }

  private sendToRemote(level: string, message: string, data?: any): void {
    // Remote logging implementasyonu
  }
}

// Her iki implementasyon da Logger interface'ini tam karşılar
const useLogger = (logger: Logger) => {
  logger.info('Test message'); // Her implementasyon için çalışır
};
```

### Interface Segregation Principle (ISP)

Büyük interface'ler küçük, spesifik interface'lere bölünmelidir.

```typescript
// ✅ Doğru yaklaşım - Küçük, spesifik interface'ler
export interface Readable<T> {
  read(id: string): Promise<T | null>;
  readAll(): Promise<T[]>;
}

export interface Writable<T> {
  create(item: T): Promise<T>;
  update(id: string, item: Partial<T>): Promise<T>;
}

export interface Deletable {
  delete(id: string): Promise<void>;
}

// Sadece ihtiyaç duyulan interface'leri implement et
export class ReadOnlyUserRepository implements Readable<User> {
  async read(id: string): Promise<User | null> {
    // Sadece okuma işlemleri
  }

  async readAll(): Promise<User[]> {
    // Sadece okuma işlemleri
  }
}

export class FullUserRepository implements Readable<User>, Writable<User>, Deletable {
  // Tüm işlemler
}

// ❌ Yanlış yaklaşım - Büyük interface
export interface UserRepository {
  read(id: string): Promise<User | null>;
  readAll(): Promise<User[]>;
  create(user: User): Promise<User>;
  update(id: string, user: Partial<User>): Promise<User>;
  delete(id: string): Promise<void>;
  backup(): Promise<void>; // Her implementasyon buna ihtiyaç duymayabilir
  restore(): Promise<void>; // Her implementasyon buna ihtiyaç duymayabilir
}
```

### Dependency Inversion Principle (DIP)

Üst seviye modüller alt seviye modüllere bağımlı olmamalıdır.

```typescript
// ✅ Doğru yaklaşım - Abstraction'lara bağımlılık
export interface EmailService {
  sendEmail(to: string, subject: string, body: string): Promise<void>;
}

export interface Logger {
  log(message: string): void;
}

export class UserService {
  constructor(
    private readonly emailService: EmailService, // Interface'e bağımlı
    private readonly logger: Logger, // Interface'e bağımlı
  ) {}

  async createUser(userData: CreateUserData): Promise<User> {
    const user = await this.saveUser(userData);

    // Interface'ler üzerinden çalışır
    await this.emailService.sendEmail(user.email, 'Hoş geldiniz', 'Hesabınız oluşturuldu');

    this.logger.log(`User created: ${user.id}`);

    return user;
  }

  private async saveUser(userData: CreateUserData): Promise<User> {
    // User kaydetme logic'i
  }
}

// Concrete implementasyonlar
export class SMTPEmailService implements EmailService {
  async sendEmail(to: string, subject: string, body: string): Promise<void> {
    // SMTP implementasyonu
  }
}

export class SendGridEmailService implements EmailService {
  async sendEmail(to: string, subject: string, body: string): Promise<void> {
    // SendGrid implementasyonu
  }
}

// Dependency injection ile bağımlılıkları çözümle
const userService = new UserService(
  new SMTPEmailService(), // İstenilen implementasyon
  new ConsoleLogger(), // İstenilen implementasyon
);
```

## Microservice Architecture Patterns

### API Gateway Pattern

Tüm API çağrıları tek bir noktadan geçer.

```typescript
// src/core/services/api/api-gateway.service.ts
export interface ApiGatewayConfig {
  baseUrl: string;
  timeout: number;
  rateLimiting: {
    enabled: boolean;
    maxRequests: number;
    windowMs: number;
  };
  authentication: {
    enabled: boolean;
    tokenHeader: string;
  };
}

export class ApiGatewayService {
  private rateLimitMap = new Map<string, number[]>();

  constructor(private config: ApiGatewayConfig) {}

  async request<T>(
    method: string,
    endpoint: string,
    data?: any,
    options?: RequestOptions,
  ): Promise<T> {
    // Rate limiting kontrolü
    if (this.config.rateLimiting.enabled) {
      this.checkRateLimit(endpoint);
    }

    // Authentication kontrolü
    if (this.config.authentication.enabled) {
      this.addAuthenticationHeaders(options);
    }

    // Logging
    this.logRequest(method, endpoint, data);

    try {
      const response = await this.makeRequest<T>(method, endpoint, data, options);
      this.logResponse(method, endpoint, response);
      return response;
    } catch (error) {
      this.logError(method, endpoint, error);
      throw error;
    }
  }

  private checkRateLimit(endpoint: string): void {
    const now = Date.now();
    const windowStart = now - this.config.rateLimiting.windowMs;

    if (!this.rateLimitMap.has(endpoint)) {
      this.rateLimitMap.set(endpoint, []);
    }

    const requests = this.rateLimitMap.get(endpoint)!;
    const recentRequests = requests.filter((time) => time > windowStart);

    if (recentRequests.length >= this.config.rateLimiting.maxRequests) {
      throw new Error('Rate limit exceeded');
    }

    recentRequests.push(now);
    this.rateLimitMap.set(endpoint, recentRequests);
  }

  private addAuthenticationHeaders(options?: RequestOptions): void {
    const token = localStorage.getItem('auth.token');
    if (token && options) {
      options.headers = {
        ...options.headers,
        [this.config.authentication.tokenHeader]: `Bearer ${token}`,
      };
    }
  }

  private logRequest(method: string, endpoint: string, data?: any): void {
    console.log(`[API Gateway] ${method} ${endpoint}`, data);
  }

  private logResponse(method: string, endpoint: string, response: any): void {
    console.log(`[API Gateway] ${method} ${endpoint} - Success`, response);
  }

  private logError(method: string, endpoint: string, error: any): void {
    console.error(`[API Gateway] ${method} ${endpoint} - Error`, error);
  }

  private async makeRequest<T>(
    method: string,
    endpoint: string,
    data?: any,
    options?: RequestOptions,
  ): Promise<T> {
    // Gerçek HTTP request implementasyonu
    const response = await fetch(`${this.config.baseUrl}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
      signal: AbortSignal.timeout(this.config.timeout),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }
}
```

### Circuit Breaker Pattern

Başarısız servis çağrıları otomatik olarak kesilir.

```typescript
// src/core/services/circuit-breaker/circuit-breaker.service.ts
export enum CircuitState {
  CLOSED = 'CLOSED', // Normal çalışma
  OPEN = 'OPEN', // Devre açık, istekler reddediliyor
  HALF_OPEN = 'HALF_OPEN', // Test aşaması
}

export interface CircuitBreakerConfig {
  failureThreshold: number; // Kaç hata sonrası devre açılsın
  resetTimeout: number; // Ne kadar süre sonra test edilsin
  monitoringPeriod: number; // İzleme periyodu
}

export class CircuitBreakerService {
  private state = CircuitState.CLOSED;
  private failureCount = 0;
  private lastFailureTime = 0;
  private successCount = 0;

  constructor(private config: CircuitBreakerConfig) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
        this.successCount = 0;
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;

    if (this.state === CircuitState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= 3) {
        // 3 başarılı deneme sonrası kapat
        this.state = CircuitState.CLOSED;
      }
    }
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.config.failureThreshold) {
      this.state = CircuitState.OPEN;
    }
  }

  private shouldAttemptReset(): boolean {
    return Date.now() - this.lastFailureTime >= this.config.resetTimeout;
  }

  getState(): CircuitState {
    return this.state;
  }

  getFailureCount(): number {
    return this.failureCount;
  }
}

// Kullanım örneği
const circuitBreaker = new CircuitBreakerService({
  failureThreshold: 5,
  resetTimeout: 60000, // 1 dakika
  monitoringPeriod: 10000, // 10 saniye
});

const apiCall = async () => {
  return circuitBreaker.execute(async () => {
    // Riskli API çağrısı
    const response = await fetch('/api/external-service');
    if (!response.ok) {
      throw new Error('API call failed');
    }
    return response.json();
  });
};
```

### Saga Pattern

Distributed transaction'lar event-driven olarak yönetilir.

```typescript
// src/core/services/saga/saga.service.ts
export interface SagaStep {
  name: string;
  execute: () => Promise<any>;
  compensate: () => Promise<void>;
}

export interface SagaDefinition {
  name: string;
  steps: SagaStep[];
}

export class SagaService {
  private executedSteps: SagaStep[] = [];

  async execute(saga: SagaDefinition): Promise<any> {
    console.log(`Starting saga: ${saga.name}`);

    try {
      for (const step of saga.steps) {
        console.log(`Executing step: ${step.name}`);
        await step.execute();
        this.executedSteps.push(step);
      }

      console.log(`Saga completed successfully: ${saga.name}`);
      return { success: true };
    } catch (error) {
      console.error(`Saga failed: ${saga.name}`, error);
      await this.compensate();
      throw error;
    }
  }

  private async compensate(): Promise<void> {
    console.log('Starting compensation...');

    // Executed steps'leri ters sırada compensate et
    for (const step of this.executedSteps.reverse()) {
      try {
        console.log(`Compensating step: ${step.name}`);
        await step.compensate();
      } catch (compensationError) {
        console.error(`Compensation failed for step: ${step.name}`, compensationError);
        // Compensation hatalarını logla ama devam et
      }
    }

    this.executedSteps = [];
    console.log('Compensation completed');
  }
}

// Kullanım örneği - E-ticaret siparişi
const createOrderSaga: SagaDefinition = {
  name: 'CreateOrder',
  steps: [
    {
      name: 'ReserveInventory',
      execute: async () => {
        // Stok rezervasyonu
        const result = await inventoryService.reserve(orderItems);
        return result;
      },
      compensate: async () => {
        // Stok rezervasyonunu iptal et
        await inventoryService.cancelReservation(orderItems);
      },
    },
    {
      name: 'ProcessPayment',
      execute: async () => {
        // Ödeme işlemi
        const result = await paymentService.charge(paymentInfo);
        return result;
      },
      compensate: async () => {
        // Ödemeyi iade et
        await paymentService.refund(paymentInfo);
      },
    },
    {
      name: 'CreateOrder',
      execute: async () => {
        // Siparişi oluştur
        const result = await orderService.create(orderData);
        return result;
      },
      compensate: async () => {
        // Siparişi iptal et
        await orderService.cancel(orderData.id);
      },
    },
  ],
};

const sagaService = new SagaService();
await sagaService.execute(createOrderSaga);
```

### CQRS Pattern

Command ve Query işlemleri ayrılır.

```typescript
// src/core/patterns/cqrs/command.ts
export interface Command {
  type: string;
  payload: any;
  timestamp: Date;
  userId?: string;
}

export interface CommandHandler<T extends Command> {
  handle(command: T): Promise<void>;
}

export class CommandBus {
  private handlers = new Map<string, CommandHandler<any>>();

  register<T extends Command>(commandType: string, handler: CommandHandler<T>): void {
    this.handlers.set(commandType, handler);
  }

  async execute<T extends Command>(command: T): Promise<void> {
    const handler = this.handlers.get(command.type);
    if (!handler) {
      throw new Error(`No handler registered for command: ${command.type}`);
    }

    await handler.handle(command);
  }
}

// src/core/patterns/cqrs/query.ts
export interface Query {
  type: string;
  parameters: any;
}

export interface QueryHandler<T extends Query, R> {
  handle(query: T): Promise<R>;
}

export class QueryBus {
  private handlers = new Map<string, QueryHandler<any, any>>();

  register<T extends Query, R>(queryType: string, handler: QueryHandler<T, R>): void {
    this.handlers.set(queryType, handler);
  }

  async execute<T extends Query, R>(query: T): Promise<R> {
    const handler = this.handlers.get(query.type);
    if (!handler) {
      throw new Error(`No handler registered for query: ${query.type}`);
    }

    return handler.handle(query);
  }
}

// Kullanım örneği
export interface CreateUserCommand extends Command {
  type: 'CREATE_USER';
  payload: {
    email: string;
    name: string;
  };
}

export class CreateUserCommandHandler implements CommandHandler<CreateUserCommand> {
  constructor(private userRepository: UserRepository) {}

  async handle(command: CreateUserCommand): Promise<void> {
    const user = new User(command.payload);
    await this.userRepository.save(user);

    // Event publish et
    await eventBus.publish(new UserCreatedEvent(user));
  }
}

export interface GetUserQuery extends Query {
  type: 'GET_USER';
  parameters: {
    id: string;
  };
}

export class GetUserQueryHandler implements QueryHandler<GetUserQuery, User> {
  constructor(private userReadModel: UserReadModel) {}

  async handle(query: GetUserQuery): Promise<User> {
    return this.userReadModel.findById(query.parameters.id);
  }
}

// Setup
const commandBus = new CommandBus();
const queryBus = new QueryBus();

commandBus.register('CREATE_USER', new CreateUserCommandHandler(userRepository));
queryBus.register('GET_USER', new GetUserQueryHandler(userReadModel));

// Kullanım
await commandBus.execute({
  type: 'CREATE_USER',
  payload: { email: '<EMAIL>', name: 'Test User' },
  timestamp: new Date(),
});

const user = await queryBus.execute({
  type: 'GET_USER',
  parameters: { id: 'user-123' },
});
```

### Event Sourcing Pattern

State değişiklikleri event olarak saklanır.

```typescript
// src/core/patterns/event-sourcing/event-store.ts
export interface DomainEvent {
  id: string;
  aggregateId: string;
  eventType: string;
  eventData: any;
  version: number;
  timestamp: Date;
}

export interface EventStore {
  saveEvents(aggregateId: string, events: DomainEvent[], expectedVersion: number): Promise<void>;
  getEvents(aggregateId: string): Promise<DomainEvent[]>;
  getAllEvents(): Promise<DomainEvent[]>;
}

export class InMemoryEventStore implements EventStore {
  private events: DomainEvent[] = [];

  async saveEvents(
    aggregateId: string,
    events: DomainEvent[],
    expectedVersion: number,
  ): Promise<void> {
    const existingEvents = this.events.filter((e) => e.aggregateId === aggregateId);
    const currentVersion = existingEvents.length;

    if (currentVersion !== expectedVersion) {
      throw new Error('Concurrency conflict');
    }

    events.forEach((event, index) => {
      event.version = currentVersion + index + 1;
      this.events.push(event);
    });
  }

  async getEvents(aggregateId: string): Promise<DomainEvent[]> {
    return this.events
      .filter((e) => e.aggregateId === aggregateId)
      .sort((a, b) => a.version - b.version);
  }

  async getAllEvents(): Promise<DomainEvent[]> {
    return [...this.events].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }
}

// Aggregate Root with Event Sourcing
export abstract class EventSourcedAggregateRoot {
  protected id: string;
  protected version = 0;
  private uncommittedEvents: DomainEvent[] = [];

  constructor(id: string) {
    this.id = id;
  }

  protected addEvent(eventType: string, eventData: any): void {
    const event: DomainEvent = {
      id: `${eventType}_${Date.now()}_${Math.random()}`,
      aggregateId: this.id,
      eventType,
      eventData,
      version: 0, // EventStore tarafından set edilecek
      timestamp: new Date(),
    };

    this.uncommittedEvents.push(event);
    this.apply(event);
  }

  getUncommittedEvents(): DomainEvent[] {
    return [...this.uncommittedEvents];
  }

  markEventsAsCommitted(): void {
    this.uncommittedEvents = [];
  }

  loadFromHistory(events: DomainEvent[]): void {
    events.forEach((event) => {
      this.apply(event);
      this.version = event.version;
    });
  }

  protected abstract apply(event: DomainEvent): void;
}

// Örnek User Aggregate
export class User extends EventSourcedAggregateRoot {
  private email: string = '';
  private name: string = '';
  private isActive: boolean = false;

  static create(id: string, email: string, name: string): User {
    const user = new User(id);
    user.addEvent('UserCreated', { email, name });
    return user;
  }

  activate(): void {
    if (this.isActive) {
      throw new Error('User is already active');
    }

    this.addEvent('UserActivated', {});
  }

  deactivate(): void {
    if (!this.isActive) {
      throw new Error('User is already inactive');
    }

    this.addEvent('UserDeactivated', {});
  }

  protected apply(event: DomainEvent): void {
    switch (event.eventType) {
      case 'UserCreated':
        this.email = event.eventData.email;
        this.name = event.eventData.name;
        this.isActive = true;
        break;
      case 'UserActivated':
        this.isActive = true;
        break;
      case 'UserDeactivated':
        this.isActive = false;
        break;
    }
  }

  // Getters
  getEmail(): string {
    return this.email;
  }
  getName(): string {
    return this.name;
  }
  getIsActive(): boolean {
    return this.isActive;
  }
}

// Repository with Event Sourcing
export class EventSourcedUserRepository {
  constructor(private eventStore: EventStore) {}

  async save(user: User): Promise<void> {
    const events = user.getUncommittedEvents();
    if (events.length > 0) {
      await this.eventStore.saveEvents(user.id, events, user.version);
      user.markEventsAsCommitted();
    }
  }

  async findById(id: string): Promise<User | null> {
    const events = await this.eventStore.getEvents(id);
    if (events.length === 0) {
      return null;
    }

    const user = new User(id);
    user.loadFromHistory(events);
    return user;
  }
}
```

Bu mimari desenler, uygulamanızın ölçeklenebilirliğini, bakımını ve güvenilirliğini artırır. Her desen belirli problemleri çözer ve doğru yerde kullanıldığında büyük faydalar sağlar.
