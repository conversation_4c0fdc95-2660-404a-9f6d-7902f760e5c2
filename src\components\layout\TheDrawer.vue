<template>
  <q-drawer v-model="layoutStore.isLeftDrawerOpen" show-if-above bordered>
    <!-- 
      Drawer i<PERSON><PERSON><PERSON><PERSON> buraya eklenebilir. 
      Örneğin, navigasyon linkleri için bir QList.
    -->
    <q-list>
      <q-item-label header> Essential Links </q-item-label>
      <!-- Buraya QItem'lar ile linkler eklenebilir -->
    </q-list>
  </q-drawer>
</template>

<script setup lang="ts">
import { useLayoutStore } from 'src/stores/layout-store';

/**
 * TheDrawer bileşeni, uygulamanın sol taraftaki çekmecesini (yan menü) temsil eder.
 * Sorumluluğu sadece görsel arayüzü sunmaktır (Dumb Component).
 * Görünürlük durumu (açık/kapalı) merkezi store'dan yönetilir.
 *
 * @see https://quasar.dev/layout/drawer
 */

// Layout durumunu yönetmek için merkezi store'u kullanıyoruz.
// `v-model` direktifini store'daki state'e ba<PERSON><PERSON>arak, Quasar'ın drawer'ı
// kapatma gibi dahili eylemlerinin bile durumu merkezi olarak güncellemesini sağlarız.
const layoutStore = useLayoutStore();
</script>
