# Fonksiyonel Programlama Yardımcıları

## Temel Fonksiyonel Yardımcılar

### Pipe ve Compose

```typescript
// src/core/utils/functional.utils.ts

// Pipe: soldan sağa fonksiyon kompozisyonu
export const pipe =
  <T>(...fns: Array<(arg: T) => T>) =>
  (value: T): T =>
    fns.reduce((acc, fn) => fn(acc), value);

// Compose: sağdan sola fonksiyon kompozisyonu
export const compose =
  <T>(...fns: Array<(arg: T) => T>) =>
  (value: T): T =>
    fns.reduceRight((acc, fn) => fn(acc), value);

// Kullanım örneği:
const processData = pipe(
  (data: string) => data.trim(),
  (data: string) => data.toLowerCase(),
  (data: string) => data.replace(/\s+/g, '-'),
);

const result = processData('  Hello World  '); // "hello-world"
```

### Curry

```typescript
// Curry: fonksiyonları kısmi uygulama için hazırlar
export const curry =
  <T extends any[], R>(fn: (...args: T) => R) =>
  (...args: Partial<T>): any =>
    args.length >= fn.length
      ? fn(...(args as T))
      : (...nextArgs: any[]) => curry(fn)(...args, ...nextArgs);

// Kullanım örneği:
const add = (a: number, b: number, c: number) => a + b + c;
const curriedAdd = curry(add);

const addFive = curriedAdd(5);
const addFiveAndThree = addFive(3);
const result = addFiveAndThree(2); // 10
```

### Memoization

```typescript
// Memoize: fonksiyon sonuçlarını önbelleğe alır
export const memoize = <T extends any[], R>(fn: (...args: T) => R): ((...args: T) => R) => {
  const cache = new Map<string, R>();

  return (...args: T): R => {
    const key = JSON.stringify(args);

    if (cache.has(key)) {
      return cache.get(key)!;
    }

    const result = fn(...args);
    cache.set(key, result);
    return result;
  };
};

// Kullanım örneği:
const expensiveCalculation = memoize((n: number): number => {
  console.log(`Calculating for ${n}`);
  return n * n * n;
});

expensiveCalculation(5); // "Calculating for 5" -> 125
expensiveCalculation(5); // 125 (önbellekten)
```

## Zaman Tabanlı Yardımcılar

### Debounce

```typescript
// Debounce: fonksiyon çağrılarını geciktirir
export const debounce = <T extends any[]>(fn: (...args: T) => void, delay: number) => {
  let timeoutId: NodeJS.Timeout;

  return (...args: T): void => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
};

// Kullanım örneği:
const searchHandler = debounce((query: string) => {
  console.log(`Searching for: ${query}`);
}, 300);

// Sadece son çağrı 300ms sonra çalışır
searchHandler('a');
searchHandler('ab');
searchHandler('abc'); // Sadece bu çalışır
```

### Throttle

```typescript
// Throttle: fonksiyon çağrılarını sınırlar
export const throttle = <T extends any[]>(fn: (...args: T) => void, limit: number) => {
  let inThrottle: boolean;

  return (...args: T): void => {
    if (!inThrottle) {
      fn(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// Kullanım örneği:
const scrollHandler = throttle(() => {
  console.log('Scroll event handled');
}, 100);

// En fazla 100ms'de bir çalışır
window.addEventListener('scroll', scrollHandler);
```

## Asenkron Yardımcılar

### Async Pipe

```typescript
// Async Pipe: asenkron fonksiyonları zincirleme
export const asyncPipe =
  <T>(...fns: Array<(arg: T) => Promise<T>>) =>
  async (value: T): Promise<T> => {
    let result = value;
    for (const fn of fns) {
      result = await fn(result);
    }
    return result;
  };

// Kullanım örneği:
const processUserData = asyncPipe(
  async (userData: UserData) => await validateUser(userData),
  async (userData: UserData) => await enrichUserData(userData),
  async (userData: UserData) => await saveUser(userData),
);

const result = await processUserData(rawUserData);
```

### Retry

```typescript
// Retry: başarısız işlemleri yeniden dener
export const retry = async <T>(
  fn: () => Promise<T>,
  attempts: number = 3,
  delay: number = 1000,
): Promise<T> => {
  try {
    return await fn();
  } catch (error) {
    if (attempts <= 1) throw error;

    await new Promise((resolve) => setTimeout(resolve, delay));
    return retry(fn, attempts - 1, delay * 2); // Exponential backoff
  }
};

// Kullanım örneği:
const fetchData = async () => {
  const response = await fetch('/api/data');
  if (!response.ok) throw new Error('Fetch failed');
  return response.json();
};

const data = await retry(fetchData, 3, 1000);
```

### Timeout

```typescript
// Timeout: promise'lere zaman aşımı ekler
export const timeout = <T>(promise: Promise<T>, ms: number): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) => setTimeout(() => reject(new Error('Operation timed out')), ms)),
  ]);
};

// Kullanım örneği:
const fetchWithTimeout = timeout(fetch('/api/slow-endpoint'), 5000);
```

## Fonksiyonel Programlama Desenleri

### Maybe Monad

```typescript
// Maybe: null/undefined değerleri güvenli şekilde işler
export const Maybe = {
  of: <T>(value: T | null | undefined) => ({
    map: <U>(fn: (value: T) => U) => (value != null ? Maybe.of(fn(value)) : Maybe.of(null)),

    flatMap: <U>(fn: (value: T) => ReturnType<typeof Maybe.of<U>>) =>
      value != null ? fn(value) : Maybe.of(null),

    filter: (predicate: (value: T) => boolean) =>
      value != null && predicate(value) ? Maybe.of(value) : Maybe.of(null),

    getOrElse: (defaultValue: T) => (value != null ? value : defaultValue),

    isSome: () => value != null,
    isNone: () => value == null,
  }),
};

// Kullanım örneği:
const user = Maybe.of(getUserById(123))
  .map((user) => user.name)
  .map((name) => name.toUpperCase())
  .filter((name) => name.length > 0)
  .getOrElse('Unknown User');
```

### Either Monad

```typescript
// Either: hata işleme için
export const Either = {
  right: <R>(value: R) => ({
    map: <U>(fn: (value: R) => U) => Either.right(fn(value)),
    flatMap: <U>(fn: (value: R) => ReturnType<typeof Either.right<U>>) => fn(value),
    mapLeft: <U>(_fn: (error: any) => U) => Either.right(value),
    fold: <U>(leftFn: (error: any) => U, rightFn: (value: R) => U) => rightFn(value),
    isRight: () => true,
    isLeft: () => false,
  }),

  left: <L>(error: L) => ({
    map: <U>(_fn: (value: any) => U) => Either.left(error),
    flatMap: <U>(_fn: (value: any) => any) => Either.left(error),
    mapLeft: <U>(fn: (error: L) => U) => Either.left(fn(error)),
    fold: <U>(leftFn: (error: L) => U, _rightFn: (value: any) => U) => leftFn(error),
    isRight: () => false,
    isLeft: () => true,
  }),
};

// Kullanım örneği:
const divideEither = (a: number, b: number) =>
  b === 0 ? Either.left('Division by zero') : Either.right(a / b);

const result = divideEither(10, 2)
  .map((x) => x * 2)
  .fold(
    (error) => `Error: ${error}`,
    (value) => `Result: ${value}`,
  ); // "Result: 10"
```

## Yardımcı Fonksiyon Koleksiyonları

### Array Utilities

```typescript
// src/core/utils/array.utils.ts
export const chunk = <T>(array: T[], size: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
};

export const unique = <T>(array: T[]): T[] => [...new Set(array)];

export const groupBy = <T, K extends keyof any>(
  array: T[],
  key: (item: T) => K,
): Record<K, T[]> => {
  return array.reduce(
    (groups, item) => {
      const groupKey = key(item);
      groups[groupKey] = groups[groupKey] || [];
      groups[groupKey].push(item);
      return groups;
    },
    {} as Record<K, T[]>,
  );
};
```

### Object Utilities

```typescript
// src/core/utils/object.utils.ts
export const pick = <T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
  const result = {} as Pick<T, K>;
  keys.forEach((key) => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
};

export const omit = <T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> => {
  const result = { ...obj };
  keys.forEach((key) => delete result[key]);
  return result;
};

export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as any;
  if (obj instanceof Array) return obj.map((item) => deepClone(item)) as any;

  const cloned = {} as T;
  Object.keys(obj).forEach((key) => {
    cloned[key as keyof T] = deepClone(obj[key as keyof T]);
  });

  return cloned;
};
```

## Performans Optimizasyonu

### Lazy Evaluation

```typescript
// Lazy: değerleri ihtiyaç duyulduğunda hesaplar
export class Lazy<T> {
  private _value: T | undefined;
  private _computed = false;

  constructor(private computation: () => T) {}

  get value(): T {
    if (!this._computed) {
      this._value = this.computation();
      this._computed = true;
    }
    return this._value!;
  }

  map<U>(fn: (value: T) => U): Lazy<U> {
    return new Lazy(() => fn(this.value));
  }
}

// Kullanım örneği:
const expensiveComputation = new Lazy(() => {
  console.log('Computing...');
  return Array.from({ length: 1000000 }, (_, i) => i).reduce((a, b) => a + b, 0);
});

// Henüz hesaplanmadı
console.log('Lazy created');

// Şimdi hesaplanıyor
console.log(expensiveComputation.value);
```

Bu fonksiyonel programlama yardımcıları, kodun daha okunabilir, test edilebilir ve sürdürülebilir olmasını sağlar. SOLID prensiplerine uygun olarak her fonksiyon tek bir sorumluluğa sahiptir ve kompozisyon yoluyla daha karmaşık işlemler oluşturulabilir.
