# Çevre <PERSON>landırması

## .env.example Do<PERSON>ı

```bash
# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
VITE_APP_NAME=MyApp
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development
VITE_APP_DEBUG=true
VITE_APP_BASE_URL=http://localhost:9000
VITE_APP_DESCRIPTION="Modern Vue.js Enterprise Application"

# =============================================================================
# DATA SOURCE CONFIGURATION
# =============================================================================
VITE_DATA_SOURCE=restapi # restapi | firebase | graphql | hybrid

# =============================================================================
# REST API CONFIGURATION
# =============================================================================
VITE_API_BASE_URL=https://api.example.com
VITE_API_VERSION=v1
VITE_API_TIMEOUT=30000
VITE_API_RETRY_COUNT=3
VITE_API_RETRY_DELAY=1000
VITE_API_CIRCUIT_BREAKER_ENABLED=true
VITE_API_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
VITE_API_CIRCUIT_BREAKER_RESET_TIMEOUT=60000
VITE_API_RATE_LIMITING_ENABLED=true
VITE_API_RATE_LIMITING_MAX_REQUESTS=100
VITE_API_RATE_LIMITING_WINDOW_MS=60000
VITE_API_COMPRESSION=true

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef
VITE_FIREBASE_MEASUREMENT_ID=G-XXXXXXXXXX

# =============================================================================
# GRAPHQL CONFIGURATION
# =============================================================================
VITE_GRAPHQL_ENDPOINT=https://api.example.com/graphql
VITE_GRAPHQL_WS_ENDPOINT=wss://api.example.com/graphql
VITE_GRAPHQL_INTROSPECTION=false
VITE_GRAPHQL_PLAYGROUND=false

# =============================================================================
# WEBSOCKET CONFIGURATION
# =============================================================================
VITE_WEBSOCKET_ENABLED=true
VITE_WEBSOCKET_URL=wss://api.example.com/ws
VITE_WEBSOCKET_RECONNECT_INTERVAL=5000
VITE_WEBSOCKET_MAX_RECONNECT_ATTEMPTS=10

# =============================================================================
# LOGGER CONFIGURATION
# =============================================================================
VITE_LOG_LEVEL=info # debug | info | warn | error
VITE_LOG_PROVIDER=console # console | remote | file | multiple
VITE_LOG_REMOTE_URL=https://logs.example.com/api/logs
VITE_LOG_REMOTE_API_KEY=your-logging-api-key
VITE_LOG_FILE_ENABLED=false
VITE_LOG_FILE_MAX_SIZE=10485760 # 10MB
VITE_LOG_FILE_MAX_FILES=5

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
VITE_NOTIFICATION_PROVIDER=quasar # quasar | push | email | multiple
VITE_NOTIFICATION_PUSH_ENABLED=true
VITE_NOTIFICATION_PUSH_VAPID_KEY=your-vapid-key
VITE_NOTIFICATION_PUSH_SERVICE_WORKER=/sw.js
VITE_NOTIFICATION_EMAIL_ENABLED=false
VITE_NOTIFICATION_EMAIL_SERVICE_URL=https://email.example.com/api/send
VITE_NOTIFICATION_EMAIL_API_KEY=your-email-api-key

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
VITE_CACHE_PROVIDER=memory # memory | localStorage | redis | hybrid
VITE_CACHE_TTL=300000 # 5 minutes
VITE_CACHE_MAX_SIZE=1000
VITE_CACHE_COMPRESSION=false
VITE_CACHE_ENCRYPTION=false

# Redis Configuration (if using redis cache)
VITE_REDIS_HOST=localhost
VITE_REDIS_PORT=6379
VITE_REDIS_PASSWORD=
VITE_REDIS_DB=0
VITE_REDIS_KEY_PREFIX=myapp:

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
VITE_AUTH_TOKEN_EXPIRY=3600000 # 1 hour
VITE_AUTH_REFRESH_TOKEN_EXPIRY=604800000 # 7 days
VITE_AUTH_SESSION_TIMEOUT=1800000 # 30 minutes
VITE_AUTH_MAX_LOGIN_ATTEMPTS=5
VITE_AUTH_LOCKOUT_DURATION=900000 # 15 minutes

# Encryption Configuration
VITE_ENCRYPTION_ALGORITHM=AES-256-GCM
VITE_ENCRYPTION_KEY_SIZE=256
VITE_ENCRYPTION_SALT_ROUNDS=12

# CSRF Configuration
VITE_CSRF_ENABLED=true
VITE_CSRF_COOKIE_NAME=XSRF-TOKEN

# CORS Configuration
VITE_CORS_ENABLED=true
VITE_CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
VITE_CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
VITE_CORS_CREDENTIALS=true

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
VITE_MONITORING_ENABLED=true

# Performance Monitoring
VITE_MONITORING_PERFORMANCE_ENABLED=true
VITE_MONITORING_PERFORMANCE_SAMPLE_RATE=0.1
VITE_MONITORING_WEB_VITALS_ENABLED=true

# Error Monitoring
VITE_MONITORING_ERRORS_ENABLED=true
VITE_MONITORING_ERRORS_REPORT_TO_REMOTE=true
VITE_MONITORING_ERRORS_REMOTE_URL=https://errors.example.com/api/report
VITE_MONITORING_ERRORS_API_KEY=your-error-tracking-api-key

# Analytics
VITE_MONITORING_ANALYTICS_ENABLED=true
VITE_MONITORING_ANALYTICS_PROVIDER=google # google | custom
VITE_MONITORING_ANALYTICS_TRACKING_ID=GA_TRACKING_ID
VITE_MONITORING_ANALYTICS_CUSTOM_ENDPOINT=https://analytics.example.com/api/track

# =============================================================================
# FEATURE FLAGS
# =============================================================================
VITE_FEATURE_REAL_TIME=true
VITE_FEATURE_OFFLINE=true
VITE_FEATURE_PWA=true
VITE_FEATURE_DARK_MODE=true
VITE_FEATURE_MULTI_LANGUAGE=true
VITE_FEATURE_NOTIFICATIONS=true
VITE_FEATURE_FILE_UPLOAD=true
VITE_FEATURE_EXPORT=true
VITE_FEATURE_ADVANCED_SEARCH=true
VITE_FEATURE_BULK_OPERATIONS=true

# =============================================================================
# THIRD PARTY INTEGRATIONS
# =============================================================================

# Google Services
VITE_GOOGLE_MAPS_API_KEY=your-google-maps-api-key
VITE_GOOGLE_RECAPTCHA_SITE_KEY=your-recaptcha-site-key

# Social Login
VITE_SOCIAL_LOGIN_GOOGLE_CLIENT_ID=your-google-client-id
VITE_SOCIAL_LOGIN_FACEBOOK_APP_ID=your-facebook-app-id
VITE_SOCIAL_LOGIN_GITHUB_CLIENT_ID=your-github-client-id

# Payment Gateways
VITE_PAYMENT_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-key
VITE_PAYMENT_PAYPAL_CLIENT_ID=your-paypal-client-id

# File Storage
VITE_STORAGE_PROVIDER=local # local | aws | gcp | azure
VITE_STORAGE_AWS_BUCKET=your-s3-bucket
VITE_STORAGE_AWS_REGION=us-east-1
VITE_STORAGE_MAX_FILE_SIZE=10485760 # 10MB
VITE_STORAGE_ALLOWED_TYPES=image/*,application/pdf,text/*

# Email Service
VITE_EMAIL_PROVIDER=smtp # smtp | sendgrid | mailgun | ses
VITE_EMAIL_FROM_ADDRESS=<EMAIL>
VITE_EMAIL_FROM_NAME=MyApp

# SMS Service
VITE_SMS_PROVIDER=twilio # twilio | aws-sns
VITE_SMS_FROM_NUMBER=+**********

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
VITE_DEV_TOOLS_ENABLED=true
VITE_DEV_MOCK_API=false
VITE_DEV_HOT_RELOAD=true
VITE_DEV_SOURCE_MAPS=true
VITE_DEV_ESLINT_ON_SAVE=true
VITE_DEV_STYLELINT_ON_SAVE=true

# =============================================================================
# BUILD CONFIGURATION
# =============================================================================
VITE_BUILD_ANALYZE=false
VITE_BUILD_MINIFY=true
VITE_BUILD_SOURCE_MAPS=false
VITE_BUILD_GZIP=true
VITE_BUILD_BROTLI=true
VITE_BUILD_LEGACY_SUPPORT=false

# =============================================================================
# PWA CONFIGURATION
# =============================================================================
VITE_PWA_ENABLED=true
VITE_PWA_NAME=MyApp
VITE_PWA_SHORT_NAME=MyApp
VITE_PWA_DESCRIPTION=Modern Vue.js Enterprise Application
VITE_PWA_THEME_COLOR=#1976d2
VITE_PWA_BACKGROUND_COLOR=#ffffff
VITE_PWA_DISPLAY=standalone
VITE_PWA_ORIENTATION=portrait
VITE_PWA_SCOPE=/
VITE_PWA_START_URL=/

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================
VITE_TEST_COVERAGE_THRESHOLD=80
VITE_TEST_MOCK_API=true
VITE_TEST_HEADLESS=true
VITE_TEST_TIMEOUT=30000
```

## Environment Type Definitions

```typescript
// src/types/env.d.ts
interface ImportMetaEnv {
  // Application
  readonly VITE_APP_NAME: string;
  readonly VITE_APP_VERSION: string;
  readonly VITE_APP_ENVIRONMENT: 'development' | 'staging' | 'production';
  readonly VITE_APP_DEBUG: string;
  readonly VITE_APP_BASE_URL: string;
  readonly VITE_APP_DESCRIPTION: string;

  // Data Source
  readonly VITE_DATA_SOURCE: 'restapi' | 'firebase' | 'graphql' | 'hybrid';

  // API
  readonly VITE_API_BASE_URL: string;
  readonly VITE_API_VERSION: string;
  readonly VITE_API_TIMEOUT: string;
  readonly VITE_API_RETRY_COUNT: string;
  readonly VITE_API_RETRY_DELAY: string;
  readonly VITE_API_CIRCUIT_BREAKER_ENABLED: string;
  readonly VITE_API_CIRCUIT_BREAKER_FAILURE_THRESHOLD: string;
  readonly VITE_API_CIRCUIT_BREAKER_RESET_TIMEOUT: string;
  readonly VITE_API_RATE_LIMITING_ENABLED: string;
  readonly VITE_API_RATE_LIMITING_MAX_REQUESTS: string;
  readonly VITE_API_RATE_LIMITING_WINDOW_MS: string;
  readonly VITE_API_COMPRESSION: string;

  // Firebase
  readonly VITE_FIREBASE_API_KEY: string;
  readonly VITE_FIREBASE_AUTH_DOMAIN: string;
  readonly VITE_FIREBASE_PROJECT_ID: string;
  readonly VITE_FIREBASE_STORAGE_BUCKET: string;
  readonly VITE_FIREBASE_MESSAGING_SENDER_ID: string;
  readonly VITE_FIREBASE_APP_ID: string;
  readonly VITE_FIREBASE_MEASUREMENT_ID: string;

  // GraphQL
  readonly VITE_GRAPHQL_ENDPOINT: string;
  readonly VITE_GRAPHQL_WS_ENDPOINT: string;
  readonly VITE_GRAPHQL_INTROSPECTION: string;
  readonly VITE_GRAPHQL_PLAYGROUND: string;

  // WebSocket
  readonly VITE_WEBSOCKET_ENABLED: string;
  readonly VITE_WEBSOCKET_URL: string;
  readonly VITE_WEBSOCKET_RECONNECT_INTERVAL: string;
  readonly VITE_WEBSOCKET_MAX_RECONNECT_ATTEMPTS: string;

  // Logger
  readonly VITE_LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error';
  readonly VITE_LOG_PROVIDER: 'console' | 'remote' | 'file' | 'multiple';
  readonly VITE_LOG_REMOTE_URL: string;
  readonly VITE_LOG_REMOTE_API_KEY: string;
  readonly VITE_LOG_FILE_ENABLED: string;
  readonly VITE_LOG_FILE_MAX_SIZE: string;
  readonly VITE_LOG_FILE_MAX_FILES: string;

  // Notification
  readonly VITE_NOTIFICATION_PROVIDER: 'quasar' | 'push' | 'email' | 'multiple';
  readonly VITE_NOTIFICATION_PUSH_ENABLED: string;
  readonly VITE_NOTIFICATION_PUSH_VAPID_KEY: string;
  readonly VITE_NOTIFICATION_PUSH_SERVICE_WORKER: string;
  readonly VITE_NOTIFICATION_EMAIL_ENABLED: string;
  readonly VITE_NOTIFICATION_EMAIL_SERVICE_URL: string;
  readonly VITE_NOTIFICATION_EMAIL_API_KEY: string;

  // Cache
  readonly VITE_CACHE_PROVIDER: 'memory' | 'localStorage' | 'redis' | 'hybrid';
  readonly VITE_CACHE_TTL: string;
  readonly VITE_CACHE_MAX_SIZE: string;
  readonly VITE_CACHE_COMPRESSION: string;
  readonly VITE_CACHE_ENCRYPTION: string;
  readonly VITE_REDIS_HOST: string;
  readonly VITE_REDIS_PORT: string;
  readonly VITE_REDIS_PASSWORD: string;
  readonly VITE_REDIS_DB: string;
  readonly VITE_REDIS_KEY_PREFIX: string;

  // Security
  readonly VITE_AUTH_TOKEN_EXPIRY: string;
  readonly VITE_AUTH_REFRESH_TOKEN_EXPIRY: string;
  readonly VITE_AUTH_SESSION_TIMEOUT: string;
  readonly VITE_AUTH_MAX_LOGIN_ATTEMPTS: string;
  readonly VITE_AUTH_LOCKOUT_DURATION: string;
  readonly VITE_ENCRYPTION_ALGORITHM: string;
  readonly VITE_ENCRYPTION_KEY_SIZE: string;
  readonly VITE_ENCRYPTION_SALT_ROUNDS: string;
  readonly VITE_CSRF_ENABLED: string;
  readonly VITE_CSRF_COOKIE_NAME: string;
  readonly VITE_CORS_ENABLED: string;
  readonly VITE_CORS_ORIGINS: string;
  readonly VITE_CORS_METHODS: string;
  readonly VITE_CORS_CREDENTIALS: string;

  // Monitoring
  readonly VITE_MONITORING_ENABLED: string;
  readonly VITE_MONITORING_PERFORMANCE_ENABLED: string;
  readonly VITE_MONITORING_PERFORMANCE_SAMPLE_RATE: string;
  readonly VITE_MONITORING_WEB_VITALS_ENABLED: string;
  readonly VITE_MONITORING_ERRORS_ENABLED: string;
  readonly VITE_MONITORING_ERRORS_REPORT_TO_REMOTE: string;
  readonly VITE_MONITORING_ERRORS_REMOTE_URL: string;
  readonly VITE_MONITORING_ERRORS_API_KEY: string;
  readonly VITE_MONITORING_ANALYTICS_ENABLED: string;
  readonly VITE_MONITORING_ANALYTICS_PROVIDER: 'google' | 'custom';
  readonly VITE_MONITORING_ANALYTICS_TRACKING_ID: string;
  readonly VITE_MONITORING_ANALYTICS_CUSTOM_ENDPOINT: string;

  // Feature Flags
  readonly VITE_FEATURE_REAL_TIME: string;
  readonly VITE_FEATURE_OFFLINE: string;
  readonly VITE_FEATURE_PWA: string;
  readonly VITE_FEATURE_DARK_MODE: string;
  readonly VITE_FEATURE_MULTI_LANGUAGE: string;
  readonly VITE_FEATURE_NOTIFICATIONS: string;
  readonly VITE_FEATURE_FILE_UPLOAD: string;
  readonly VITE_FEATURE_EXPORT: string;
  readonly VITE_FEATURE_ADVANCED_SEARCH: string;
  readonly VITE_FEATURE_BULK_OPERATIONS: string;

  // Third Party
  readonly VITE_GOOGLE_MAPS_API_KEY: string;
  readonly VITE_GOOGLE_RECAPTCHA_SITE_KEY: string;
  readonly VITE_SOCIAL_LOGIN_GOOGLE_CLIENT_ID: string;
  readonly VITE_SOCIAL_LOGIN_FACEBOOK_APP_ID: string;
  readonly VITE_SOCIAL_LOGIN_GITHUB_CLIENT_ID: string;
  readonly VITE_PAYMENT_STRIPE_PUBLISHABLE_KEY: string;
  readonly VITE_PAYMENT_PAYPAL_CLIENT_ID: string;
  readonly VITE_STORAGE_PROVIDER: 'local' | 'aws' | 'gcp' | 'azure';
  readonly VITE_STORAGE_AWS_BUCKET: string;
  readonly VITE_STORAGE_AWS_REGION: string;
  readonly VITE_STORAGE_MAX_FILE_SIZE: string;
  readonly VITE_STORAGE_ALLOWED_TYPES: string;
  readonly VITE_EMAIL_PROVIDER: 'smtp' | 'sendgrid' | 'mailgun' | 'ses';
  readonly VITE_EMAIL_FROM_ADDRESS: string;
  readonly VITE_EMAIL_FROM_NAME: string;
  readonly VITE_SMS_PROVIDER: 'twilio' | 'aws-sns';
  readonly VITE_SMS_FROM_NUMBER: string;

  // Development
  readonly VITE_DEV_TOOLS_ENABLED: string;
  readonly VITE_DEV_MOCK_API: string;
  readonly VITE_DEV_HOT_RELOAD: string;
  readonly VITE_DEV_SOURCE_MAPS: string;
  readonly VITE_DEV_ESLINT_ON_SAVE: string;
  readonly VITE_DEV_STYLELINT_ON_SAVE: string;

  // Build
  readonly VITE_BUILD_ANALYZE: string;
  readonly VITE_BUILD_MINIFY: string;
  readonly VITE_BUILD_SOURCE_MAPS: string;
  readonly VITE_BUILD_GZIP: string;
  readonly VITE_BUILD_BROTLI: string;
  readonly VITE_BUILD_LEGACY_SUPPORT: string;

  // PWA
  readonly VITE_PWA_ENABLED: string;
  readonly VITE_PWA_NAME: string;
  readonly VITE_PWA_SHORT_NAME: string;
  readonly VITE_PWA_DESCRIPTION: string;
  readonly VITE_PWA_THEME_COLOR: string;
  readonly VITE_PWA_BACKGROUND_COLOR: string;
  readonly VITE_PWA_DISPLAY: string;
  readonly VITE_PWA_ORIENTATION: string;
  readonly VITE_PWA_SCOPE: string;
  readonly VITE_PWA_START_URL: string;

  // Testing
  readonly VITE_TEST_COVERAGE_THRESHOLD: string;
  readonly VITE_TEST_MOCK_API: string;
  readonly VITE_TEST_HEADLESS: string;
  readonly VITE_TEST_TIMEOUT: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
```

## Environment Configuration Service

```typescript
// src/config/env.config.ts
export class EnvConfig {
  // Helper methods for type conversion
  private static getBoolean(value: string | undefined, defaultValue: boolean = false): boolean {
    if (!value) return defaultValue;
    return value.toLowerCase() === 'true';
  }

  private static getNumber(value: string | undefined, defaultValue: number = 0): number {
    if (!value) return defaultValue;
    const num = parseInt(value, 10);
    return isNaN(num) ? defaultValue : num;
  }

  private static getString(value: string | undefined, defaultValue: string = ''): string {
    return value || defaultValue;
  }

  private static getArray(value: string | undefined, separator: string = ','): string[] {
    if (!value) return [];
    return value
      .split(separator)
      .map((item) => item.trim())
      .filter(Boolean);
  }

  // Application Configuration
  static get app() {
    return {
      name: this.getString(import.meta.env.VITE_APP_NAME, 'MyApp'),
      version: this.getString(import.meta.env.VITE_APP_VERSION, '1.0.0'),
      environment: import.meta.env.VITE_APP_ENVIRONMENT || 'development',
      debug: this.getBoolean(import.meta.env.VITE_APP_DEBUG, true),
      baseUrl: this.getString(import.meta.env.VITE_APP_BASE_URL, 'http://localhost:9000'),
      description: this.getString(import.meta.env.VITE_APP_DESCRIPTION, ''),
    };
  }

  // API Configuration
  static get api() {
    return {
      dataSource: import.meta.env.VITE_DATA_SOURCE || 'restapi',
      baseURL: this.getString(import.meta.env.VITE_API_BASE_URL, 'https://api.example.com'),
      version: this.getString(import.meta.env.VITE_API_VERSION, 'v1'),
      timeout: this.getNumber(import.meta.env.VITE_API_TIMEOUT, 30000),
      retryCount: this.getNumber(import.meta.env.VITE_API_RETRY_COUNT, 3),
      retryDelay: this.getNumber(import.meta.env.VITE_API_RETRY_DELAY, 1000),
      circuitBreaker: {
        enabled: this.getBoolean(import.meta.env.VITE_API_CIRCUIT_BREAKER_ENABLED, true),
        failureThreshold: this.getNumber(
          import.meta.env.VITE_API_CIRCUIT_BREAKER_FAILURE_THRESHOLD,
          5,
        ),
        resetTimeout: this.getNumber(import.meta.env.VITE_API_CIRCUIT_BREAKER_RESET_TIMEOUT, 60000),
      },
      rateLimiting: {
        enabled: this.getBoolean(import.meta.env.VITE_API_RATE_LIMITING_ENABLED, true),
        maxRequests: this.getNumber(import.meta.env.VITE_API_RATE_LIMITING_MAX_REQUESTS, 100),
        windowMs: this.getNumber(import.meta.env.VITE_API_RATE_LIMITING_WINDOW_MS, 60000),
      },
      compression: this.getBoolean(import.meta.env.VITE_API_COMPRESSION, true),
    };
  }

  // Firebase Configuration
  static get firebase() {
    return {
      apiKey: this.getString(import.meta.env.VITE_FIREBASE_API_KEY),
      authDomain: this.getString(import.meta.env.VITE_FIREBASE_AUTH_DOMAIN),
      projectId: this.getString(import.meta.env.VITE_FIREBASE_PROJECT_ID),
      storageBucket: this.getString(import.meta.env.VITE_FIREBASE_STORAGE_BUCKET),
      messagingSenderId: this.getString(import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID),
      appId: this.getString(import.meta.env.VITE_FIREBASE_APP_ID),
      measurementId: this.getString(import.meta.env.VITE_FIREBASE_MEASUREMENT_ID),
    };
  }

  // GraphQL Configuration
  static get graphql() {
    return {
      endpoint: this.getString(import.meta.env.VITE_GRAPHQL_ENDPOINT),
      wsEndpoint: this.getString(import.meta.env.VITE_GRAPHQL_WS_ENDPOINT),
      introspection: this.getBoolean(import.meta.env.VITE_GRAPHQL_INTROSPECTION, false),
      playground: this.getBoolean(import.meta.env.VITE_GRAPHQL_PLAYGROUND, false),
    };
  }

  // WebSocket Configuration
  static get websocket() {
    return {
      enabled: this.getBoolean(import.meta.env.VITE_WEBSOCKET_ENABLED, true),
      url: this.getString(import.meta.env.VITE_WEBSOCKET_URL),
      reconnectInterval: this.getNumber(import.meta.env.VITE_WEBSOCKET_RECONNECT_INTERVAL, 5000),
      maxReconnectAttempts: this.getNumber(
        import.meta.env.VITE_WEBSOCKET_MAX_RECONNECT_ATTEMPTS,
        10,
      ),
    };
  }

  // Logger Configuration
  static get logger() {
    return {
      level: import.meta.env.VITE_LOG_LEVEL || 'info',
      provider: import.meta.env.VITE_LOG_PROVIDER || 'console',
      remote: {
        url: this.getString(import.meta.env.VITE_LOG_REMOTE_URL),
        apiKey: this.getString(import.meta.env.VITE_LOG_REMOTE_API_KEY),
      },
      file: {
        enabled: this.getBoolean(import.meta.env.VITE_LOG_FILE_ENABLED, false),
        maxSize: this.getNumber(import.meta.env.VITE_LOG_FILE_MAX_SIZE, 10485760),
        maxFiles: this.getNumber(import.meta.env.VITE_LOG_FILE_MAX_FILES, 5),
      },
    };
  }

  // Cache Configuration
  static get cache() {
    return {
      provider: import.meta.env.VITE_CACHE_PROVIDER || 'memory',
      ttl: this.getNumber(import.meta.env.VITE_CACHE_TTL, 300000),
      maxSize: this.getNumber(import.meta.env.VITE_CACHE_MAX_SIZE, 1000),
      compression: this.getBoolean(import.meta.env.VITE_CACHE_COMPRESSION, false),
      encryption: this.getBoolean(import.meta.env.VITE_CACHE_ENCRYPTION, false),
      redis: {
        host: this.getString(import.meta.env.VITE_REDIS_HOST, 'localhost'),
        port: this.getNumber(import.meta.env.VITE_REDIS_PORT, 6379),
        password: this.getString(import.meta.env.VITE_REDIS_PASSWORD),
        db: this.getNumber(import.meta.env.VITE_REDIS_DB, 0),
        keyPrefix: this.getString(import.meta.env.VITE_REDIS_KEY_PREFIX, 'myapp:'),
      },
    };
  }

  // Security Configuration
  static get security() {
    return {
      auth: {
        tokenExpiry: this.getNumber(import.meta.env.VITE_AUTH_TOKEN_EXPIRY, 3600000),
        refreshTokenExpiry: this.getNumber(
          import.meta.env.VITE_AUTH_REFRESH_TOKEN_EXPIRY,
          604800000,
        ),
        sessionTimeout: this.getNumber(import.meta.env.VITE_AUTH_SESSION_TIMEOUT, 1800000),
        maxLoginAttempts: this.getNumber(import.meta.env.VITE_AUTH_MAX_LOGIN_ATTEMPTS, 5),
        lockoutDuration: this.getNumber(import.meta.env.VITE_AUTH_LOCKOUT_DURATION, 900000),
      },
      encryption: {
        algorithm: this.getString(import.meta.env.VITE_ENCRYPTION_ALGORITHM, 'AES-256-GCM'),
        keySize: this.getNumber(import.meta.env.VITE_ENCRYPTION_KEY_SIZE, 256),
        saltRounds: this.getNumber(import.meta.env.VITE_ENCRYPTION_SALT_ROUNDS, 12),
      },
      csrf: {
        enabled: this.getBoolean(import.meta.env.VITE_CSRF_ENABLED, true),
        cookieName: this.getString(import.meta.env.VITE_CSRF_COOKIE_NAME, 'XSRF-TOKEN'),
      },
      cors: {
        enabled: this.getBoolean(import.meta.env.VITE_CORS_ENABLED, true),
        origins: this.getArray(import.meta.env.VITE_CORS_ORIGINS),
        methods: this.getArray(import.meta.env.VITE_CORS_METHODS),
        credentials: this.getBoolean(import.meta.env.VITE_CORS_CREDENTIALS, true),
      },
    };
  }

  // Monitoring Configuration
  static get monitoring() {
    return {
      enabled: this.getBoolean(import.meta.env.VITE_MONITORING_ENABLED, true),
      performance: {
        enabled: this.getBoolean(import.meta.env.VITE_MONITORING_PERFORMANCE_ENABLED, true),
        sampleRate: parseFloat(import.meta.env.VITE_MONITORING_PERFORMANCE_SAMPLE_RATE || '0.1'),
        webVitalsEnabled: this.getBoolean(import.meta.env.VITE_MONITORING_WEB_VITALS_ENABLED, true),
      },
      errors: {
        enabled: this.getBoolean(import.meta.env.VITE_MONITORING_ERRORS_ENABLED, true),
        reportToRemote: this.getBoolean(
          import.meta.env.VITE_MONITORING_ERRORS_REPORT_TO_REMOTE,
          true,
        ),
        remoteUrl: this.getString(import.meta.env.VITE_MONITORING_ERRORS_REMOTE_URL),
        apiKey: this.getString(import.meta.env.VITE_MONITORING_ERRORS_API_KEY),
      },
      analytics: {
        enabled: this.getBoolean(import.meta.env.VITE_MONITORING_ANALYTICS_ENABLED, true),
        provider: import.meta.env.VITE_MONITORING_ANALYTICS_PROVIDER || 'google',
        trackingId: this.getString(import.meta.env.VITE_MONITORING_ANALYTICS_TRACKING_ID),
        customEndpoint: this.getString(import.meta.env.VITE_MONITORING_ANALYTICS_CUSTOM_ENDPOINT),
      },
    };
  }

  // Feature Flags
  static get features() {
    return {
      realTime: this.getBoolean(import.meta.env.VITE_FEATURE_REAL_TIME, true),
      offline: this.getBoolean(import.meta.env.VITE_FEATURE_OFFLINE, true),
      pwa: this.getBoolean(import.meta.env.VITE_FEATURE_PWA, true),
      darkMode: this.getBoolean(import.meta.env.VITE_FEATURE_DARK_MODE, true),
      multiLanguage: this.getBoolean(import.meta.env.VITE_FEATURE_MULTI_LANGUAGE, true),
      notifications: this.getBoolean(import.meta.env.VITE_FEATURE_NOTIFICATIONS, true),
      fileUpload: this.getBoolean(import.meta.env.VITE_FEATURE_FILE_UPLOAD, true),
      export: this.getBoolean(import.meta.env.VITE_FEATURE_EXPORT, true),
      advancedSearch: this.getBoolean(import.meta.env.VITE_FEATURE_ADVANCED_SEARCH, true),
      bulkOperations: this.getBoolean(import.meta.env.VITE_FEATURE_BULK_OPERATIONS, true),
    };
  }

  // Development Configuration
  static get development() {
    return {
      toolsEnabled: this.getBoolean(import.meta.env.VITE_DEV_TOOLS_ENABLED, true),
      mockApi: this.getBoolean(import.meta.env.VITE_DEV_MOCK_API, false),
      hotReload: this.getBoolean(import.meta.env.VITE_DEV_HOT_RELOAD, true),
      sourceMaps: this.getBoolean(import.meta.env.VITE_DEV_SOURCE_MAPS, true),
      eslintOnSave: this.getBoolean(import.meta.env.VITE_DEV_ESLINT_ON_SAVE, true),
      stylelintOnSave: this.getBoolean(import.meta.env.VITE_DEV_STYLELINT_ON_SAVE, true),
    };
  }

  // Validation
  static validate(): void {
    const errors: string[] = [];

    // Required fields validation
    if (!this.app.name) {
      errors.push('VITE_APP_NAME is required');
    }

    if (this.api.dataSource === 'firebase' && !this.firebase.apiKey) {
      errors.push('VITE_FIREBASE_API_KEY is required when using Firebase');
    }

    if (this.api.dataSource === 'graphql' && !this.graphql.endpoint) {
      errors.push('VITE_GRAPHQL_ENDPOINT is required when using GraphQL');
    }

    if (errors.length > 0) {
      throw new Error(`Environment validation failed:\n${errors.join('\n')}`);
    }
  }

  // Get all configuration
  static getAll() {
    return {
      app: this.app,
      api: this.api,
      firebase: this.firebase,
      graphql: this.graphql,
      websocket: this.websocket,
      logger: this.logger,
      cache: this.cache,
      security: this.security,
      monitoring: this.monitoring,
      features: this.features,
      development: this.development,
    };
  }
}
```

## Environment-specific Files

### Development (.env.development)

```bash
# Development specific overrides
VITE_APP_ENVIRONMENT=development
VITE_APP_DEBUG=true
VITE_API_BASE_URL=http://localhost:3001
VITE_LOG_LEVEL=debug
VITE_DEV_TOOLS_ENABLED=true
VITE_DEV_MOCK_API=true
VITE_MONITORING_ENABLED=false
```

### Staging (.env.staging)

```bash
# Staging specific overrides
VITE_APP_ENVIRONMENT=staging
VITE_APP_DEBUG=false
VITE_API_BASE_URL=https://staging-api.example.com
VITE_LOG_LEVEL=info
VITE_DEV_TOOLS_ENABLED=false
VITE_MONITORING_ENABLED=true
VITE_MONITORING_PERFORMANCE_SAMPLE_RATE=0.5
```

### Production (.env.production)

```bash
# Production specific overrides
VITE_APP_ENVIRONMENT=production
VITE_APP_DEBUG=false
VITE_API_BASE_URL=https://api.example.com
VITE_LOG_LEVEL=warn
VITE_DEV_TOOLS_ENABLED=false
VITE_MONITORING_ENABLED=true
VITE_MONITORING_PERFORMANCE_SAMPLE_RATE=0.1
VITE_BUILD_MINIFY=true
VITE_BUILD_SOURCE_MAPS=false
```

## Environment Validation Composable

```typescript
// src/core/composables/useEnv.ts
export const useEnv = () => {
  const config = EnvConfig.getAll();

  const isDevelopment = computed(() => config.app.environment === 'development');
  const isStaging = computed(() => config.app.environment === 'staging');
  const isProduction = computed(() => config.app.environment === 'production');
  const isDebug = computed(() => config.app.debug);

  const hasFeature = (feature: keyof typeof config.features): boolean => {
    return config.features[feature];
  };

  const getApiUrl = (endpoint: string): string => {
    const baseUrl = config.api.baseURL.replace(/\/$/, '');
    const version = config.api.version ? `/${config.api.version}` : '';
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${baseUrl}${version}${cleanEndpoint}`;
  };

  return {
    config: readonly(ref(config)),
    isDevelopment,
    isStaging,
    isProduction,
    isDebug,
    hasFeature,
    getApiUrl,
  };
};
```
