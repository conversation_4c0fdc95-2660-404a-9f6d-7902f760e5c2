{"name": "boilerplate", "version": "0.0.1", "description": "Laser Engraver", "productName": "Laser Engraver", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "type": "module", "private": true, "scripts": {"lint": "eslint -c ./eslint.config.js \"./src*/**/*.{ts,js,cjs,mjs,vue}\"", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build", "postinstall": "quasar prepare"}, "dependencies": {"axios": "^1.2.1", "vue-i18n": "^11.0.0", "pinia": "^3.0.1", "@quasar/extras": "^1.16.4", "quasar": "^2.16.0", "vue": "^3.4.18", "vue-router": "^4.0.12"}, "devDependencies": {"@eslint/js": "^9.14.0", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "globals": "^15.12.0", "vue-tsc": "^2.0.29", "@vue/eslint-config-typescript": "^14.4.0", "vite-plugin-checker": "^0.9.0", "@vue/eslint-config-prettier": "^10.1.0", "prettier": "^3.3.3", "@types/node": "^20.5.9", "@intlify/unplugin-vue-i18n": "^4.0.0", "@quasar/app-vite": "^2.1.0", "autoprefixer": "^10.4.2", "typescript": "~5.5.3"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}