<template>
  <q-btn flat round icon="language">
    <q-menu auto-close>
      <q-list>
        <q-item
          v-for="lang in languageOptions"
          :key="lang.value"
          clickable
          @click="languageStore.setLocale(lang.value)"
          :active="languageStore.locale === lang.value"
          active-class="text-primary"
        >
          <q-item-section>{{ lang.label }}</q-item-section>
          <q-item-section side v-if="languageStore.locale === lang.value">
            <q-icon name="check" />
          </q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useLanguageStore, type LanguageOption } from 'src/stores/language-store';

/**
 * LanguageToggle bileşeni, uygulamanın dilini değiştirmek için bir UI sağlar.
 * <PERSON><PERSON>, <PERSON>k <PERSON>ibi (SRP) gereği sadece dil seçimi arayüzünü sunar.
 * Dil değiştirme mantığı ve durumu useLanguageStore Pinia store'unda yönetilir.
 * Bu sayede bileşen "Dumb Component" olarak kalır ve iş mantığı ile UI ayrımı sağlanır.
 *
 * Açık/Kapalı Prensibi (OCP) gereği, yeni diller eklendiğinde bu bileşenin değişmesine gerek kalmaz,
 * sadece language-store'daki supportedLocales listesi güncellenir.
 */

const { locale } = useI18n(); // vue-i18n'dan mevcut locale'i alıyoruz
const languageStore = useLanguageStore(); // Pinia dil store'umuzu kullanıyoruz

/**
 * languageOptions computed property'si, QMenu içindeki dil seçeneklerini oluşturmak için kullanılır.
 * Dil store'undan desteklenen dilleri alır.
 */
const languageOptions = computed<LanguageOption[]>(() => {
  return languageStore.supportedLocales;
});

/**
 * Watcher, Pinia store'daki dil (locale) değiştiğinde vue-i18n'ın locale'ini günceller.
 * Bu, uygulamanın genel çeviri dilinin Pinia store ile senkronize kalmasını sağlar.
 */
watch(
  () => languageStore.locale,
  (newLocale) => {
    locale.value = newLocale;
  },
  { immediate: true },
); // Bileşen yüklendiğinde hemen çalıştır
</script>
