# Test Yapısı ve Stratejileri

## Test Klasör <PERSON>

```
src/tests/
├── unit/                    # Birim testleri
│   ├── core/               # Core katmanı testleri
│   │   ├── services/       # Servis testleri
│   │   ├── utils/          # Utility testleri
│   │   └── composables/    # Composable testleri
│   ├── modules/            # Modül testleri
│   │   ├── auth/           # Auth modülü testleri
│   │   └── dashboard/      # Dashboard modülü testleri
│   └── shared/             # Paylaşılan bileşen testleri
├── integration/            # Entegrasyon testleri
│   ├── api/                # API entegrasyon testleri
│   ├── database/           # Veritabanı testleri
│   └── services/           # Servis entegrasyon testleri
├── e2e/                    # End-to-end testleri
│   ├── auth/               # Kimlik doğrulama akışları
│   ├── dashboard/          # Dashboard kullanıcı akışları
│   └── user-flows/         # Kullanıcı senaryoları
├── fixtures/               # Test verileri
├── mocks/                  # Mock dosyaları
└── utils/                  # Test yardımcı fonksiyonları
```

## Test Konfigürasyonu

### Vitest Konfigürasyonu

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import { quasar, transformAssetUrls } from '@quasar/vite-plugin';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
  plugins: [
    vue({
      template: { transformAssetUrls },
    }),
    quasar({
      sassVariables: 'src/quasar-variables.sass',
    }),
  ],
  test: {
    globals: true,
    environment: 'happy-dom',
    setupFiles: ['src/tests/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: ['node_modules/', 'src/tests/', '**/*.d.ts', '**/*.config.*', '**/coverage/**'],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@tests': resolve(__dirname, './src/tests'),
    },
  },
});
```

### Test Setup Dosyası

```typescript
// src/tests/setup.ts
import { config } from '@vue/test-utils';
import { Quasar } from 'quasar';
import { createTestingPinia } from '@pinia/testing';
import { vi } from 'vitest';

// Quasar konfigürasyonu
config.global.plugins = [Quasar];

// Pinia test konfigürasyonu
config.global.plugins.push(
  createTestingPinia({
    createSpy: vi.fn,
  }),
);

// Global mocks
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// LocalStorage mock
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
vi.stubGlobal('localStorage', localStorageMock);

// Fetch mock
global.fetch = vi.fn();

// Console mock (sessiz testler için)
if (process.env.NODE_ENV === 'test') {
  console.warn = vi.fn();
  console.error = vi.fn();
}
```

## Test Yardımcı Fonksiyonları

### Test Utilities

```typescript
// src/tests/utils/test-utils.ts
import { mount, VueWrapper } from '@vue/test-utils';
import { Quasar } from 'quasar';
import { createTestingPinia } from '@pinia/testing';
import { vi } from 'vitest';
import type { Component } from 'vue';

export interface TestWrapperOptions {
  props?: Record<string, any>;
  slots?: Record<string, any>;
  global?: {
    plugins?: any[];
    provide?: Record<string, any>;
    mocks?: Record<string, any>;
  };
}

export const createTestWrapper = (
  component: Component,
  options: TestWrapperOptions = {},
): VueWrapper => {
  return mount(component, {
    global: {
      plugins: [
        Quasar,
        createTestingPinia({ createSpy: vi.fn }),
        ...(options.global?.plugins || []),
      ],
      provide: {
        logger: {
          debug: vi.fn(),
          info: vi.fn(),
          warn: vi.fn(),
          error: vi.fn(),
        },
        notification: {
          show: vi.fn(),
          success: vi.fn(),
          error: vi.fn(),
          warning: vi.fn(),
          info: vi.fn(),
        },
        ...(options.global?.provide || {}),
      },
      mocks: {
        $t: (key: string) => key,
        $router: {
          push: vi.fn(),
          replace: vi.fn(),
          go: vi.fn(),
          back: vi.fn(),
          forward: vi.fn(),
        },
        $route: {
          path: '/',
          params: {},
          query: {},
          meta: {},
        },
        ...(options.global?.mocks || {}),
      },
    },
    props: options.props,
    slots: options.slots,
  });
};

export const waitForNextTick = () => new Promise((resolve) => setTimeout(resolve, 0));

export const flushPromises = () => new Promise((resolve) => setTimeout(resolve, 0));
```

### Mock Factory

```typescript
// src/tests/utils/mock-factory.ts
import { vi } from 'vitest';
import type { User, AuthResponse } from '@/modules/auth/types/auth.types';

export const createMockUser = (overrides: Partial<User> = {}): User => ({
  id: 'user-123',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  fullName: 'Test User',
  roles: ['user'],
  permissions: ['read'],
  isActive: true,
  emailVerified: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

export const createMockAuthResponse = (overrides: Partial<AuthResponse> = {}): AuthResponse => ({
  user: createMockUser(),
  token: 'mock-jwt-token',
  refreshToken: 'mock-refresh-token',
  expiresIn: 3600,
  ...overrides,
});

export const createMockApiService = () => ({
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  patch: vi.fn(),
});

export const createMockLogger = () => ({
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  time: vi.fn(() => vi.fn()),
});

export const createMockNotification = () => ({
  show: vi.fn(),
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn(),
  loading: vi.fn(() => ({
    dismiss: vi.fn(),
    update: vi.fn(),
  })),
});
```

## Birim Test Örnekleri

### Servis Testi

```typescript
// src/tests/unit/modules/auth/services/auth.service.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AuthService } from '@/modules/auth/services/auth.service';
import {
  createMockApiService,
  createMockLogger,
  createMockAuthResponse,
} from '@tests/utils/mock-factory';
import type { LoginCredentials } from '@/modules/auth/types/auth.types';

describe('AuthService', () => {
  let authService: AuthService;
  let mockApiService: ReturnType<typeof createMockApiService>;
  let mockLogger: ReturnType<typeof createMockLogger>;

  beforeEach(() => {
    mockApiService = createMockApiService();
    mockLogger = createMockLogger();
    authService = new AuthService(mockApiService as any, mockLogger as any);
  });

  describe('login', () => {
    it('başarılı giriş yapmalı', async () => {
      // Arrange
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const mockResponse = createMockAuthResponse();
      mockApiService.post.mockResolvedValue(mockResponse);

      // Act
      const result = await authService.login(credentials);

      // Assert
      expect(mockApiService.post).toHaveBeenCalledWith('/auth/login', {
        email: credentials.email,
        password: credentials.password,
        rememberMe: undefined,
      });
      expect(result).toEqual(mockResponse);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Login successful',
        { userId: mockResponse.user.id },
        'AuthService',
      );
    });

    it('hatalı giriş durumunda hata fırlatmalı', async () => {
      // Arrange
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'wrong-password',
      };
      const mockError = new Error('Invalid credentials');
      mockApiService.post.mockRejectedValue(mockError);

      // Act & Assert
      await expect(authService.login(credentials)).rejects.toThrow('Invalid credentials');
      expect(mockLogger.error).toHaveBeenCalledWith('Login failed', mockError, 'AuthService');
    });
  });

  describe('register', () => {
    it('başarılı kayıt yapmalı', async () => {
      // Arrange
      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        firstName: 'Test',
        lastName: 'User',
        acceptTerms: true,
      };
      const mockResponse = createMockAuthResponse();
      mockApiService.post.mockResolvedValue(mockResponse);

      // Act
      const result = await authService.register(registerData);

      // Assert
      expect(mockApiService.post).toHaveBeenCalledWith('/auth/register', {
        email: registerData.email,
        password: registerData.password,
        firstName: registerData.firstName,
        lastName: registerData.lastName,
      });
      expect(result).toEqual(mockResponse);
    });
  });
});
```

### Composable Testi

```typescript
// src/tests/unit/core/composables/useApi.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useApi } from '@/core/composables/useApi';
import { createMockApiService } from '@tests/utils/mock-factory';

// Mock useCore composable
vi.mock('@/core/composables/useCore', () => ({
  useCore: () => ({
    api: createMockApiService(),
    cache: {
      get: vi.fn(),
      set: vi.fn(),
    },
    logger: {
      debug: vi.fn(),
    },
    handleError: vi.fn(),
    safeAsync: vi.fn((fn) => fn()),
  }),
}));

describe('useApi', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('GET isteği yapmalı', async () => {
    // Arrange
    const { get } = useApi();
    const mockData = { id: 1, name: 'Test' };

    // Act
    const result = await get('/test');

    // Assert
    expect(result).toBeDefined();
  });

  it('POST isteği yapmalı', async () => {
    // Arrange
    const { post } = useApi();
    const mockData = { name: 'Test' };
    const expectedResponse = { id: 1, ...mockData };

    // Act
    const result = await post('/test', mockData);

    // Assert
    expect(result).toBeDefined();
  });
});
```

### Component Testi

```typescript
// src/tests/unit/modules/auth/components/LoginForm.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createTestWrapper } from '@tests/utils/test-utils';
import LoginForm from '@/modules/auth/components/forms/LoginForm.vue';

// Mock composables
vi.mock('@/modules/auth/composables/useAuth', () => ({
  useAuth: () => ({
    loginAndRedirect: vi.fn(),
    isLoading: false,
  }),
}));

vi.mock('@/core/composables/useValidation', () => ({
  useValidation: () => ({
    useForm: () => ({
      values: {
        email: '',
        password: '',
        rememberMe: false,
      },
      errors: {
        email: [],
        password: [],
      },
      isValid: true,
      validate: vi.fn().mockResolvedValue(true),
    }),
  }),
}));

describe('LoginForm', () => {
  let wrapper: ReturnType<typeof createTestWrapper>;

  beforeEach(() => {
    wrapper = createTestWrapper(LoginForm);
  });

  it('doğru şekilde render edilmeli', () => {
    expect(wrapper.find('.login-form').exists()).toBe(true);
    expect(wrapper.find('input[type="email"]').exists()).toBe(true);
    expect(wrapper.find('input[type="password"]').exists()).toBe(true);
    expect(wrapper.find('button[type="submit"]').exists()).toBe(true);
  });

  it('form gönderildiğinde login fonksiyonu çağrılmalı', async () => {
    // Arrange
    const emailInput = wrapper.find('input[type="email"]');
    const passwordInput = wrapper.find('input[type="password"]');
    const submitButton = wrapper.find('button[type="submit"]');

    // Act
    await emailInput.setValue('<EMAIL>');
    await passwordInput.setValue('password123');
    await submitButton.trigger('click');

    // Assert
    // Login fonksiyonunun çağrıldığını kontrol et
    expect(wrapper.emitted('success')).toBeDefined();
  });

  it('geçersiz form durumunda submit butonu devre dışı olmalı', async () => {
    // Arrange
    wrapper = createTestWrapper(LoginForm, {
      global: {
        provide: {
          useValidation: () => ({
            useForm: () => ({
              values: { email: '', password: '', rememberMe: false },
              errors: { email: ['Email gerekli'], password: [] },
              isValid: false,
              validate: vi.fn(),
            }),
          }),
        },
      },
    });

    // Assert
    const submitButton = wrapper.find('button[type="submit"]');
    expect(submitButton.attributes('disabled')).toBeDefined();
  });
});
```

## Entegrasyon Testleri

### API Entegrasyon Testi

```typescript
// src/tests/integration/api/auth.api.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { setupServer } from 'msw/node';
import { rest } from 'msw';
import { AuthService } from '@/modules/auth/services/auth.service';
import { createApiService } from '@/core/services/api/api.factory';
import { createMockLogger } from '@tests/utils/mock-factory';

// Mock server setup
const server = setupServer(
  rest.post('/auth/login', (req, res, ctx) => {
    return res(
      ctx.json({
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
        expiresIn: 3600,
      }),
    );
  }),

  rest.post('/auth/register', (req, res, ctx) => {
    return res(
      ctx.json({
        user: {
          id: 'user-456',
          email: '<EMAIL>',
          firstName: 'New',
          lastName: 'User',
        },
        token: 'mock-jwt-token-new',
        refreshToken: 'mock-refresh-token-new',
        expiresIn: 3600,
      }),
    );
  }),
);

describe('Auth API Integration', () => {
  let authService: AuthService;

  beforeEach(() => {
    server.listen();
    const apiService = createApiService(
      {
        baseURL: '',
        timeout: 5000,
        retryCount: 1,
        retryDelay: 100,
      } as any,
      createMockLogger() as any,
    );
    authService = new AuthService(apiService, createMockLogger() as any);
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  it('gerçek API ile login yapabilmeli', async () => {
    // Arrange
    const credentials = {
      email: '<EMAIL>',
      password: 'password123',
    };

    // Act
    const result = await authService.login(credentials);

    // Assert
    expect(result.user.email).toBe('<EMAIL>');
    expect(result.token).toBe('mock-jwt-token');
  });

  it('gerçek API ile register yapabilmeli', async () => {
    // Arrange
    const registerData = {
      email: '<EMAIL>',
      password: 'password123',
      confirmPassword: 'password123',
      firstName: 'New',
      lastName: 'User',
      acceptTerms: true,
    };

    // Act
    const result = await authService.register(registerData);

    // Assert
    expect(result.user.email).toBe('<EMAIL>');
    expect(result.token).toBe('mock-jwt-token-new');
  });
});
```

## E2E Testleri

### Playwright Konfigürasyonu

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './src/tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:9000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:9000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### E2E Test Örneği

```typescript
// src/tests/e2e/auth/login.e2e.test.ts
import { test, expect } from '@playwright/test';

test.describe('Login Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/auth/login');
  });

  test('başarılı giriş yapabilmeli', async ({ page }) => {
    // Arrange
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');

    // Act
    await page.click('[data-testid="login-button"]');

    // Assert
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });

  test('geçersiz kimlik bilgileri ile hata göstermeli', async ({ page }) => {
    // Arrange
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');

    // Act
    await page.click('[data-testid="login-button"]');

    // Assert
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Giriş yapılamadı');
  });

  test('form validasyonu çalışmalı', async ({ page }) => {
    // Act
    await page.click('[data-testid="login-button"]');

    // Assert
    await expect(page.locator('[data-testid="email-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-error"]')).toBeVisible();
  });

  test('şifremi unuttum linki çalışmalı', async ({ page }) => {
    // Act
    await page.click('[data-testid="forgot-password-link"]');

    // Assert
    await expect(page).toHaveURL('/auth/forgot-password');
  });
});
```

## Test Komutları

### Package.json Scripts

```json
{
  "scripts": {
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:run": "vitest run",
    "test:coverage": "vitest run --coverage",
    "test:watch": "vitest --watch",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:e2e:debug": "playwright test --debug",
    "test:all": "npm run test:run && npm run test:e2e"
  }
}
```

## Test Best Practices

### 1. Test Organizasyonu

- Her test dosyası tek bir modül/bileşeni test etmeli
- Test dosyaları kaynak kodla aynı klasör yapısını takip etmeli
- Açıklayıcı test isimleri kullanın

### 2. Test Yazma Prensipleri

- AAA pattern (Arrange, Act, Assert) kullanın
- Her test bağımsız olmalı
- Mock'ları doğru şekilde kullanın
- Edge case'leri test edin

### 3. Performance

- Test süitlerini hızlı tutun
- Gereksiz async/await kullanmayın
- Paralel test çalıştırma kullanın

### 4. Maintenance

- Test verilerini fixture'larda tutun
- Tekrarlanan kodu helper fonksiyonlara çıkarın
- Test'leri düzenli olarak güncelleyin

Bu test yapısı, uygulamanızın kalitesini garanti altına alır ve sürekli entegrasyon süreçlerinizi destekler.
