/**
 * Konfigürasyon Tipleri ve Arayüzleri
 * 
 * Bu dosya tüm konfigürasyon tiplerini ve interface'lerini içerir.
 * TypeScript strict mode ile tip güvenli yapı sağlar.
 * 
 * <AUTHOR> Architecture Team
 * @version 1.0.0
 */

// ============================================================================
// TEMEL TİPLER (Basic Types)
// ============================================================================

/**
 * Veri kaynağı tipleri - Uygulamanın hangi veri kaynağını kullanacağını belirler
 */
export type DataSource = 'restapi' | 'firebase' | 'graphql' | 'hybrid';

/**
 * Log seviyeleri - Loglama detay seviyesini belirler
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * Bildirim sağlayıcı tipleri - Hangi bildirim sisteminin kullanılacağını belirler
 */
export type NotificationProvider = 'quasar' | 'push' | 'email' | 'multiple';

/**
 * Önbellek sağlayıcı tipleri - Hangi önbellek sisteminin kullanılacağını belirler
 */
export type CacheProvider = 'memory' | 'localStorage' | 'redis' | 'hybrid';

/**
 * Ortam tipleri - Uygulamanın çalıştığı ortamı belirler
 */
export type Environment = 'development' | 'staging' | 'production';

/**
 * Logger sağlayıcı tipleri - Hangi logger sisteminin kullanılacağını belirler
 */
export type LoggerProvider = 'console' | 'remote' | 'file' | 'multiple';

/**
 * Şifreleme algoritması tipleri
 */
export type EncryptionAlgorithm = 'AES-256-GCM' | 'AES-256-CBC' | 'ChaCha20-Poly1305';

// ============================================================================
// UYGULAMA KONFİGÜRASYONU (Application Configuration)
// ============================================================================

/**
 * Uygulama özellik bayrakları - Hangi özelliklerin aktif olacağını belirler
 */
export interface AppFeatures {
  /** Gerçek zamanlı özellikler aktif mi? */
  readonly realTime: boolean;
  /** Çevrimdışı çalışma desteği aktif mi? */
  readonly offline: boolean;
  /** Analitik takip aktif mi? */
  readonly analytics: boolean;
  /** Performans izleme aktif mi? */
  readonly monitoring: boolean;
  /** PWA özellikleri aktif mi? */
  readonly pwa: boolean;
}

/**
 * Ana uygulama konfigürasyon arayüzü
 */
export interface AppConfig {
  /** Uygulama adı */
  readonly name: string;
  /** Uygulama versiyonu */
  readonly version: string;
  /** Çalışma ortamı */
  readonly environment: Environment;
  /** Debug modu aktif mi? */
  readonly debug: boolean;
  /** Uygulama temel URL'i */
  readonly baseUrl: string;
  /** API versiyonu */
  readonly apiVersion: string;
  /** Uygulama özellikleri */
  readonly features: AppFeatures;
  /** Uygulama başlığı (SEO için) */
  readonly title: string;
  /** Uygulama açıklaması (SEO için) */
  readonly description: string;
}

// ============================================================================
// API KONFİGÜRASYONU (API Configuration)
// ============================================================================

/**
 * Devre kesici (Circuit Breaker) konfigürasyonu
 */
export interface CircuitBreakerConfig {
  /** Devre kesici aktif mi? */
  readonly enabled: boolean;
  /** Hata eşiği - Bu kadar hata sonrası devre açılır */
  readonly failureThreshold: number;
  /** Sıfırlama zaman aşımı (ms) */
  readonly resetTimeout: number;
  /** İzleme penceresi (ms) */
  readonly monitoringPeriod: number;
}

/**
 * Hız sınırlama (Rate Limiting) konfigürasyonu
 */
export interface RateLimitingConfig {
  /** Hız sınırlama aktif mi? */
  readonly enabled: boolean;
  /** Maksimum istek sayısı */
  readonly maxRequests: number;
  /** Zaman penceresi (ms) */
  readonly windowMs: number;
  /** Sınır aşıldığında bekleme süresi (ms) */
  readonly retryAfter: number;
}

/**
 * WebSocket konfigürasyonu
 */
export interface WebSocketConfig {
  /** WebSocket aktif mi? */
  readonly enabled: boolean;
  /** WebSocket URL'i */
  readonly url: string;
  /** Yeniden bağlanma aralığı (ms) */
  readonly reconnectInterval: number;
  /** Maksimum yeniden bağlanma denemesi */
  readonly maxReconnectAttempts: number;
  /** Heartbeat aralığı (ms) */
  readonly heartbeatInterval: number;
}

/**
 * API konfigürasyon arayüzü
 */
export interface ApiConfig {
  /** Veri kaynağı tipi */
  readonly dataSource: DataSource;
  /** API temel URL'i */
  readonly baseURL: string;
  /** İstek zaman aşımı (ms) */
  readonly timeout: number;
  /** Yeniden deneme sayısı */
  readonly retryCount: number;
  /** Yeniden deneme gecikmesi (ms) */
  readonly retryDelay: number;
  /** Devre kesici ayarları */
  readonly circuitBreaker: CircuitBreakerConfig;
  /** Hız sınırlama ayarları */
  readonly rateLimiting: RateLimitingConfig;
  /** Sıkıştırma aktif mi? */
  readonly compression: boolean;
  /** WebSocket ayarları (opsiyonel) */
  readonly websocket?: WebSocketConfig;
  /** API anahtarı (opsiyonel) */
  readonly apiKey?: string;
}

// ============================================================================
// GÜVENLİK KONFİGÜRASYONU (Security Configuration)
// ============================================================================

/**
 * Kimlik doğrulama konfigürasyonu
 */
export interface AuthConfig {
  /** Token geçerlilik süresi (ms) */
  readonly tokenExpiry: number;
  /** Yenileme token geçerlilik süresi (ms) */
  readonly refreshTokenExpiry: number;
  /** Oturum zaman aşımı (ms) */
  readonly sessionTimeout: number;
  /** Maksimum giriş denemesi */
  readonly maxLoginAttempts: number;
  /** Hesap kilitleme süresi (ms) */
  readonly lockoutDuration: number;
}

/**
 * Şifreleme konfigürasyonu
 */
export interface EncryptionConfig {
  /** Şifreleme algoritması */
  readonly algorithm: EncryptionAlgorithm;
  /** Anahtar boyutu */
  readonly keySize: number;
  /** Salt rounds (bcrypt için) */
  readonly saltRounds: number;
  /** IV boyutu */
  readonly ivSize: number;
}

/**
 * CSRF koruması konfigürasyonu
 */
export interface CsrfConfig {
  /** CSRF koruması aktif mi? */
  readonly enabled: boolean;
  /** CSRF cookie adı */
  readonly cookieName: string;
  /** Cookie güvenli mi? (HTTPS) */
  readonly secure: boolean;
  /** SameSite ayarı */
  readonly sameSite: 'strict' | 'lax' | 'none';
}

/**
 * CORS konfigürasyonu
 */
export interface CorsConfig {
  /** CORS aktif mi? */
  readonly enabled: boolean;
  /** İzin verilen origin'ler */
  readonly origins: readonly string[];
  /** İzin verilen HTTP metodları */
  readonly methods: readonly string[];
  /** İzin verilen header'lar */
  readonly allowedHeaders: readonly string[];
  /** Credentials'a izin var mı? */
  readonly credentials: boolean;
}

/**
 * Güvenlik konfigürasyon arayüzü
 */
export interface SecurityConfig {
  /** Kimlik doğrulama ayarları */
  readonly auth: AuthConfig;
  /** Şifreleme ayarları */
  readonly encryption: EncryptionConfig;
  /** CSRF koruması ayarları */
  readonly csrf: CsrfConfig;
  /** CORS ayarları */
  readonly cors: CorsConfig;
}

// ============================================================================
// LOGGER KONFİGÜRASYONU (Logger Configuration)
// ============================================================================

/**
 * Logger konfigürasyon arayüzü
 */
export interface LoggerConfig {
  /** Log seviyesi */
  readonly level: LogLevel;
  /** Logger sağlayıcısı */
  readonly provider: LoggerProvider;
  /** Uzak log URL'i (remote logger için) */
  readonly remoteUrl?: string;
  /** Log dosyası yolu (file logger için) */
  readonly filePath?: string;
  /** Maksimum log dosyası boyutu (bytes) */
  readonly maxFileSize: number;
  /** Maksimum log dosyası sayısı */
  readonly maxFiles: number;
  /** Yapılandırılmış loglama aktif mi? */
  readonly structured: boolean;
}

// ============================================================================
// FIREBASE KONFİGÜRASYONU (Firebase Configuration)
// ============================================================================

/**
 * Firebase konfigürasyon arayüzü
 */
export interface FirebaseConfig {
  /** Firebase API anahtarı */
  readonly apiKey: string;
  /** Auth domain */
  readonly authDomain: string;
  /** Proje ID */
  readonly projectId: string;
  /** Storage bucket */
  readonly storageBucket: string;
  /** Messaging sender ID */
  readonly messagingSenderId: string;
  /** App ID */
  readonly appId: string;
  /** Measurement ID (Analytics için, opsiyonel) */
  readonly measurementId?: string;
}

// ============================================================================
// BİLDİRİM KONFİGÜRASYONU (Notification Configuration)
// ============================================================================

/**
 * Push bildirim konfigürasyonu
 */
export interface PushNotificationConfig {
  /** VAPID public key */
  readonly vapidKey: string;
  /** Service worker dosyası yolu */
  readonly serviceWorkerPath: string;
}

/**
 * Email bildirim konfigürasyonu
 */
export interface EmailNotificationConfig {
  /** Email servis URL'i */
  readonly serviceUrl: string;
  /** API anahtarı */
  readonly apiKey: string;
  /** Varsayılan gönderen */
  readonly defaultSender: string;
}

/**
 * Bildirim konfigürasyon arayüzü
 */
export interface NotificationConfig {
  /** Bildirim sağlayıcısı */
  readonly provider: NotificationProvider;
  /** Push bildirim ayarları (opsiyonel) */
  readonly push?: PushNotificationConfig;
  /** Email bildirim ayarları (opsiyonel) */
  readonly email?: EmailNotificationConfig;
  /** Varsayılan bildirim süresi (ms) */
  readonly defaultDuration: number;
  /** Maksimum eşzamanlı bildirim sayısı */
  readonly maxConcurrent: number;
}

// ============================================================================
// ÖNBELLEK KONFİGÜRASYONU (Cache Configuration)
// ============================================================================

/**
 * Redis konfigürasyonu
 */
export interface RedisConfig {
  /** Redis host */
  readonly host: string;
  /** Redis port */
  readonly port: number;
  /** Redis şifresi (opsiyonel) */
  readonly password?: string;
  /** Redis veritabanı numarası */
  readonly db: number;
  /** Bağlantı zaman aşımı (ms) */
  readonly connectTimeout: number;
}

/**
 * Önbellek konfigürasyon arayüzü
 */
export interface CacheConfig {
  /** Önbellek sağlayıcısı */
  readonly provider: CacheProvider;
  /** Varsayılan TTL (ms) */
  readonly defaultTTL: number;
  /** Maksimum önbellek boyutu */
  readonly maxSize: number;
  /** Redis ayarları (opsiyonel) */
  readonly redis?: RedisConfig;
  /** LRU çıkarma politikası aktif mi? */
  readonly lruEviction: boolean;
}

// ============================================================================
// İZLEME KONFİGÜRASYONU (Monitoring Configuration)
// ============================================================================

/**
 * Performans izleme konfigürasyonu
 */
export interface PerformanceMonitoringConfig {
  /** Performans izleme aktif mi? */
  readonly enabled: boolean;
  /** Örnekleme oranı (0-1 arası) */
  readonly sampleRate: number;
  /** Metrik toplama aralığı (ms) */
  readonly collectInterval: number;
}

/**
 * Hata izleme konfigürasyonu
 */
export interface ErrorMonitoringConfig {
  /** Hata izleme aktif mi? */
  readonly enabled: boolean;
  /** Uzak hata raporlama URL'i */
  readonly remoteUrl?: string;
  /** Maksimum hata raporu boyutu */
  readonly maxReportSize: number;
}

/**
 * Analitik konfigürasyonu
 */
export interface AnalyticsConfig {
  /** Analitik aktif mi? */
  readonly enabled: boolean;
  /** Analitik sağlayıcısı */
  readonly provider: 'google' | 'mixpanel' | 'custom';
  /** Takip ID'si */
  readonly trackingId?: string;
  /** Örnekleme oranı */
  readonly sampleRate: number;
}

/**
 * İzleme konfigürasyon arayüzü
 */
export interface MonitoringConfig {
  /** Genel izleme aktif mi? */
  readonly enabled: boolean;
  /** Performans izleme ayarları */
  readonly performance: PerformanceMonitoringConfig;
  /** Hata izleme ayarları */
  readonly errors: ErrorMonitoringConfig;
  /** Analitik ayarları */
  readonly analytics: AnalyticsConfig;
}

// ============================================================================
// ANA KONFİGÜRASYON ARAYÜZÜ (Core Configuration Interface)
// ============================================================================

/**
 * Ana konfigürasyon arayüzü - Tüm alt konfigürasyonları birleştirir
 */
export interface CoreConfig {
  /** Uygulama konfigürasyonu */
  readonly app: AppConfig;
  /** API konfigürasyonu */
  readonly api: ApiConfig;
  /** Firebase konfigürasyonu */
  readonly firebase: FirebaseConfig;
  /** Logger konfigürasyonu */
  readonly logger: LoggerConfig;
  /** Bildirim konfigürasyonu */
  readonly notification: NotificationConfig;
  /** Güvenlik konfigürasyonu */
  readonly security: SecurityConfig;
  /** Önbellek konfigürasyonu */
  readonly cache: CacheConfig;
  /** İzleme konfigürasyonu */
  readonly monitoring: MonitoringConfig;
}

// ============================================================================
// DOĞRULAMA TİPLERİ (Validation Types)
// ============================================================================

/**
 * Doğrulama hatası arayüzü
 */
export interface ValidationError {
  /** Hata kodu */
  readonly code: string;
  /** Hata mesajı */
  readonly message: string;
  /** Hatalı alan yolu */
  readonly path: string;
  /** Hatalı değer */
  readonly value: unknown;
}

/**
 * Doğrulama sonucu arayüzü
 */
export interface ValidationResult {
  /** Doğrulama başarılı mı? */
  readonly isValid: boolean;
  /** Doğrulama hataları */
  readonly errors: readonly ValidationError[];
  /** Uyarılar (opsiyonel) */
  readonly warnings?: readonly ValidationError[];
}

// ============================================================================
// YARDIMCI TİPLER (Utility Types)
// ============================================================================

/**
 * Konfigürasyon factory fonksiyon tipi
 */
export type ConfigFactory<T> = () => T;

/**
 * Konfigürasyon doğrulayıcı fonksiyon tipi
 */
export type ConfigValidator<T> = (config: T) => ValidationResult;

/**
 * Ortam değişkeni değeri tipi
 */
export type EnvValue = string | number | boolean | undefined;

/**
 * Ortam değişkeni haritası tipi
 */
export type EnvMap = Record<string, EnvValue>;
