import { defineStore } from 'pinia';
import { ref } from 'vue';
import { Quasar } from 'quasar';
import langEnUS from 'quasar/lang/en-US';
import langTr from 'quasar/lang/tr';

/**
 * @interface LanguageOption
 * Dil seçici bileşeninde kullanılacak dil seçeneği arayüzü.
 * @property {string} label - <PERSON>lin görünen adı (örn: 'English', 'Türkçe').
 * @property {string} value - <PERSON><PERSON> kodu (örn: 'en-US', 'tr-TR').
 */
export interface LanguageOption {
    label: string;
    value: string;
}

/**
 * useLanguageStore Pinia store'u, uygulamanın dil durumunu yönetir.
 * Tek Sorumluluk Prensibi (SRP) gereği, dil yönetimiyle ilgili tüm mantık burada toplanmıştır.
 * Bu sayede diğer bileşenler veya servisler dil yönetimi detaylarıyla ilgilenmek zorunda kalmaz.
 */
export const useLanguageStore = defineStore('language', () => {
    // Mevcut aktif dil kodu. localStorage'dan veya varsayılan olarak 'en-US' ile başlar.
    const locale = ref<string>(localStorage.getItem('app-locale') || 'en-US');

    // Uygulama tarafından desteklenen dillerin listesi.
    // Bu liste, src/i18n/index.ts dosyasından dinamik olarak alınabilir.
    // Şimdilik sabit olarak tanımlanmıştır.
    const supportedLocales: LanguageOption[] = [
        { label: 'English', value: 'en-US' },
        { label: 'Türkçe', value: 'tr-TR' },
    ];

    /**
     * setLocale eylemi, uygulamanın dilini değiştirir.
     * @param {string} newLocale - Ayarlanacak yeni dil kodu.
     *
     * SOLID Prensibi: Bu eylem, dil durumunu güncelleme ve kalıcılığını sağlama sorumluluğunu taşır (SRP).
     * Ayrıca, Quasar'ın kendi dil ayarını da güncelleyerek Quasar bileşenlerinin de yeni dile uyum sağlamasını sağlar.
     */
    const setLocale = (newLocale: string) => {
        if (supportedLocales.some(lang => lang.value === newLocale)) {
            locale.value = newLocale;
            localStorage.setItem('app-locale', newLocale);
            // Quasar'ın kendi dil ayarını güncelle
            Quasar.lang.set(newLocale === 'en-US' ? langEnUS : langTr);
        } else {
            console.warn(`Desteklenmeyen dil kodu: ${newLocale}`);
        }
    };

    /**
     * initializeLocale eylemi, uygulama başladığında veya yenilendiğinde
     * kaydedilmiş dil ayarını yükler.
     *
     * SOLID Prensibi: Bu eylem, başlangıç dilini yükleme sorumluluğunu taşır (SRP).
     */
    const initializeLocale = () => {
        const savedLocale = localStorage.getItem('app-locale');
        if (savedLocale && supportedLocales.some(lang => lang.value === savedLocale)) {
            locale.value = savedLocale;
            // Quasar'ın kendi dil ayarını güncelle
            Quasar.lang.set(savedLocale === 'en-US' ? langEnUS : langTr);
        } else {
            // Varsayılan dili ayarla
            setLocale('en-US');
        }
    };

    return {
        locale,
        supportedLocales,
        setLocale,
        initializeLocale,
    };
});