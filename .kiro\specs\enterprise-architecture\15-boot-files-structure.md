# Boot Dosyaları Yapısı

## Ana Boot <PERSON>ası

```typescript
// src/boot/core.boot.ts
import { boot } from 'quasar/wrappers';
import { EnvConfig } from '@/config/env.config';
import { createCoreServices } from '@/core/providers/core.provider';

export default boot(({ app }) => {
  // Validate environment configuration
  EnvConfig.validate();

  // Get configuration
  const config = EnvConfig.getAll();

  // Create and provide core services
  const coreServices = createCoreServices(config);

  // Provide services to the app
  Object.entries(coreServices).forEach(([key, service]) => {
    app.provide(key, service);
  });

  // Global error handler
  app.config.errorHandler = async (error, instance, info) => {
    const errorHandler = coreServices.errorHandler;
    const appError = await errorHandler.handle(error, {
      component: instance?.$options.name || 'Unknown',
      info,
      route: instance?.$route?.path,
    });

    await errorHandler.report(appError);
    await errorHandler.recover(appError);
  };

  // Global warning handler
  app.config.warnHandler = (msg, instance, trace) => {
    const logger = coreServices.logger;
    logger.warn(
      'Vue Warning',
      {
        message: msg,
        component: instance?.$options.name,
        trace,
      },
      'VueWarningHandler',
    );
  };

  // Performance monitoring
  if (config.monitoring.enabled) {
    const performance = coreServices.performance;

    // Track app initialization
    const initTimer = performance.startTimer('app-initialization');

    app.mixin({
      mounted() {
        if (this.$options.name === 'App') {
          initTimer();
        }
      },
    });
  }
});
```

## API Boot Dosyası

```typescript
// src/boot/api.boot.ts
import { boot } from 'quasar/wrappers';
import { createApiService } from '@/core/services/api/api.factory';
import { EnvConfig } from '@/config/env.config';

export default boot(({ app }) => {
  const config = EnvConfig.api;
  const logger = app.config.globalProperties.$logger;

  // Create API service based on configuration
  const apiService = createApiService(config, logger);

  // Provide API service
  app.provide('apiService', apiService);
  app.config.globalProperties.$api = apiService;

  // Setup request/response interceptors
  if (apiService.setupInterceptors) {
    apiService.setupInterceptors({
      request: (config) => {
        // Add auth token
        const token = localStorage.getItem('auth.token');
        if (token) {
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${token}`,
          };
        }

        // Add request ID for tracking
        config.headers = {
          ...config.headers,
          'X-Request-ID': `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        };

        return config;
      },
      response: (response) => {
        // Log successful responses in debug mode
        if (EnvConfig.app.debug) {
          logger.debug(
            'API Response',
            {
              url: response.config.url,
              status: response.status,
              data: response.data,
            },
            'ApiInterceptor',
          );
        }
        return response;
      },
      error: (error) => {
        // Handle 401 - Unauthorized
        if (error.response?.status === 401) {
          // Clear auth data and redirect to login
          localStorage.removeItem('auth.token');
          localStorage.removeItem('auth.user');
          window.location.href = '/auth/login';
        }

        // Handle 403 - Forbidden
        if (error.response?.status === 403) {
          logger.warn(
            'Access forbidden',
            {
              url: error.config?.url,
              method: error.config?.method,
            },
            'ApiInterceptor',
          );
        }

        // Handle network errors
        if (!error.response) {
          logger.error(
            'Network error',
            {
              message: error.message,
              url: error.config?.url,
            },
            'ApiInterceptor',
          );
        }

        return Promise.reject(error);
      },
    });
  }
});
```

## Logger Boot Dosyası

```typescript
// src/boot/logger.boot.ts
import { boot } from 'quasar/wrappers';
import { createLoggerService } from '@/core/services/logger/logger.factory';
import { EnvConfig } from '@/config/env.config';

export default boot(({ app }) => {
  const config = EnvConfig.logger;

  // Create logger service
  const logger = createLoggerService(config);

  // Provide logger service
  app.provide('logger', logger);
  app.config.globalProperties.$logger = logger;

  // Setup global console override in production
  if (EnvConfig.app.environment === 'production') {
    const originalConsole = { ...console };

    console.log = (...args) => {
      logger.info(args.join(' '), {}, 'Console');
      if (EnvConfig.app.debug) {
        originalConsole.log(...args);
      }
    };

    console.warn = (...args) => {
      logger.warn(args.join(' '), {}, 'Console');
      if (EnvConfig.app.debug) {
        originalConsole.warn(...args);
      }
    };

    console.error = (...args) => {
      logger.error(args.join(' '), {}, 'Console');
      if (EnvConfig.app.debug) {
        originalConsole.error(...args);
      }
    };
  }

  // Log application start
  logger.info(
    'Application starting',
    {
      version: EnvConfig.app.version,
      environment: EnvConfig.app.environment,
      timestamp: new Date().toISOString(),
    },
    'Application',
  );
});
```

## Authentication Boot Dosyası

```typescript
// src/boot/auth.boot.ts
import { boot } from 'quasar/wrappers';
import { useAuthStore } from '@/modules/auth/stores/auth.store';

export default boot(async ({ app, router }) => {
  const authStore = useAuthStore();

  // Initialize authentication state
  await authStore.initializeAuth();

  // Setup router guards
  router.beforeEach(async (to, from, next) => {
    // Check if route requires authentication
    if (to.meta.requiresAuth && !authStore.isAuthenticated) {
      next({
        path: '/auth/login',
        query: { redirect: to.fullPath },
      });
      return;
    }

    // Check if route requires guest (unauthenticated user)
    if (to.meta.requiresGuest && authStore.isAuthenticated) {
      next('/dashboard');
      return;
    }

    // Check role requirements
    if (to.meta.requiresRole && authStore.isAuthenticated) {
      const requiredRole = to.meta.requiresRole as string;
      if (!authStore.userRoles.includes(requiredRole)) {
        next('/403'); // Forbidden page
        return;
      }
    }

    // Check permission requirements
    if (to.meta.requiresPermission && authStore.isAuthenticated) {
      const requiredPermission = to.meta.requiresPermission as string;
      if (!authStore.userPermissions.includes(requiredPermission)) {
        next('/403'); // Forbidden page
        return;
      }
    }

    // Update last activity
    if (authStore.isAuthenticated) {
      authStore.updateLastActivity();
    }

    next();
  });

  // Setup automatic token refresh
  if (authStore.isAuthenticated) {
    const refreshInterval = setInterval(
      async () => {
        if (authStore.isAuthenticated) {
          const success = await authStore.refreshAuthToken();
          if (!success) {
            clearInterval(refreshInterval);
          }
        } else {
          clearInterval(refreshInterval);
        }
      },
      15 * 60 * 1000,
    ); // 15 minutes

    // Clear interval on app unmount
    app.config.globalProperties.$refreshInterval = refreshInterval;
  }

  // Setup session timeout
  let sessionTimeoutId: NodeJS.Timeout;

  const resetSessionTimeout = () => {
    if (sessionTimeoutId) {
      clearTimeout(sessionTimeoutId);
    }

    if (authStore.isAuthenticated) {
      sessionTimeoutId = setTimeout(async () => {
        await authStore.logout();
        router.push('/auth/login?reason=session-timeout');
      }, EnvConfig.security.auth.sessionTimeout);
    }
  };

  // Reset timeout on user activity
  const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

  activityEvents.forEach((event) => {
    document.addEventListener(event, resetSessionTimeout, true);
  });

  // Initial timeout setup
  resetSessionTimeout();
});
```

## i18n Boot Dosyası

```typescript
// src/boot/i18n.boot.ts
import { boot } from 'quasar/wrappers';
import { createI18n } from 'vue-i18n';
import { Quasar } from 'quasar';

// Import language files
import enUS from '@/i18n/en-US.json';
import trTR from '@/i18n/tr-TR.json';

// Import Quasar language packs
import langEnUS from 'quasar/lang/en-US';
import langTrTR from 'quasar/lang/tr';

const messages = {
  'en-US': enUS,
  'tr-TR': trTR,
};

const quasarLangMap = {
  'en-US': langEnUS,
  'tr-TR': langTrTR,
};

export default boot(({ app }) => {
  // Get saved locale or use browser default
  const savedLocale = localStorage.getItem('app.locale');
  const browserLocale = navigator.language || 'en-US';
  const defaultLocale = savedLocale || browserLocale;

  // Ensure we have the locale in our messages
  const locale = messages[defaultLocale as keyof typeof messages] ? defaultLocale : 'en-US';

  // Create i18n instance
  const i18n = createI18n({
    locale,
    fallbackLocale: 'en-US',
    messages,
    legacy: false,
    globalInjection: true,
  });

  // Set Quasar language
  Quasar.lang.set(quasarLangMap[locale as keyof typeof quasarLangMap] || langEnUS);

  // Use i18n
  app.use(i18n);

  // Provide locale switching function
  const switchLocale = (newLocale: string) => {
    if (messages[newLocale as keyof typeof messages]) {
      i18n.global.locale.value = newLocale;
      localStorage.setItem('app.locale', newLocale);

      // Update Quasar language
      const quasarLang = quasarLangMap[newLocale as keyof typeof quasarLangMap];
      if (quasarLang) {
        Quasar.lang.set(quasarLang);
      }

      // Update document language
      document.documentElement.lang = newLocale;
    }
  };

  // Set initial document language
  document.documentElement.lang = locale;

  // Provide locale utilities
  app.provide('switchLocale', switchLocale);
  app.config.globalProperties.$switchLocale = switchLocale;
});
```

## Notification Boot Dosyası

```typescript
// src/boot/notification.boot.ts
import { boot } from 'quasar/wrappers';
import { createNotificationService } from '@/core/services/notification/notification.factory';
import { EnvConfig } from '@/config/env.config';

export default boot(({ app }) => {
  const config = EnvConfig.getAll();

  // Create notification service
  const notificationService = createNotificationService(config.notification);

  // Provide notification service
  app.provide('notification', notificationService);
  app.config.globalProperties.$notify = notificationService;

  // Setup push notifications if enabled
  if (config.features.notifications && 'serviceWorker' in navigator) {
    navigator.serviceWorker.ready.then(async (registration) => {
      try {
        // Request notification permission
        const permission = await Notification.requestPermission();

        if (permission === 'granted') {
          // Subscribe to push notifications
          const subscription = await registration.pushManager.subscribe({
            userVisibleOnly: true,
            applicationServerKey: config.notification.push?.vapidKey,
          });

          // Send subscription to server
          if (subscription) {
            // You would typically send this to your backend
            console.log('Push subscription:', subscription);
          }
        }
      } catch (error) {
        console.warn('Push notification setup failed:', error);
      }
    });
  }

  // Setup global notification handlers
  window.addEventListener('online', () => {
    notificationService.show({
      type: 'positive',
      message: 'İnternet bağlantısı yeniden kuruldu',
      timeout: 3000,
    });
  });

  window.addEventListener('offline', () => {
    notificationService.show({
      type: 'negative',
      message: 'İnternet bağlantısı kesildi',
      persistent: true,
    });
  });
});
```

## Performance Boot Dosyası

```typescript
// src/boot/performance.boot.ts
import { boot } from 'quasar/wrappers';
import { createPerformanceMonitor } from '@/core/services/performance/performance-monitor.service';
import { createWebVitalsService } from '@/core/services/performance/web-vitals.service';
import { createResourceMonitorService } from '@/core/services/performance/resource-monitor.service';
import { createMemoryMonitorService } from '@/core/services/performance/memory-monitor.service';
import { EnvConfig } from '@/config/env.config';

export default boot(({ app, router }) => {
  const config = EnvConfig.monitoring;

  if (!config.enabled) return;

  // Create performance services
  const performanceMonitor = createPerformanceMonitor();
  const webVitals = createWebVitalsService(performanceMonitor);
  const resourceMonitor = createResourceMonitorService(performanceMonitor);
  const memoryMonitor = createMemoryMonitorService(performanceMonitor);

  // Provide services
  app.provide('performance', performanceMonitor);
  app.provide('webVitals', webVitals);
  app.provide('resourceMonitor', resourceMonitor);
  app.provide('memoryMonitor', memoryMonitor);

  // Start monitoring
  if (config.performance.webVitalsEnabled) {
    webVitals.startMonitoring();
  }

  resourceMonitor.startMonitoring();
  memoryMonitor.startMonitoring();

  // Track route changes
  router.beforeEach((to, from, next) => {
    if (from.name) {
      const routeTimer = performanceMonitor.startTimer('route-change', {
        from: from.path,
        to: to.path,
      });

      router.afterEach(() => {
        routeTimer();
      });
    }
    next();
  });

  // Track component performance
  app.mixin({
    beforeCreate() {
      if (this.$options.name && config.performance.enabled) {
        this._componentTimer = performanceMonitor.startTimer(`component-${this.$options.name}`, {
          component: this.$options.name,
          type: 'lifecycle',
        });
      }
    },
    mounted() {
      if (this._componentTimer) {
        this._componentTimer();
      }
    },
  });

  // Memory leak detection
  if (EnvConfig.app.debug) {
    setInterval(() => {
      if (memoryMonitor.detectMemoryLeaks()) {
        console.warn('Potential memory leak detected!');
        performanceMonitor.recordMetric({
          name: 'memory-leak-detected',
          value: 1,
          timestamp: Date.now(),
          tags: { type: 'warning' },
        });
      }
    }, 60000); // Check every minute
  }
});
```

## PWA Boot Dosyası

```typescript
// src/boot/pwa.boot.ts
import { boot } from 'quasar/wrappers';
import { EnvConfig } from '@/config/env.config';

export default boot(({ app }) => {
  const config = EnvConfig.getAll();

  if (!config.features.pwa) return;

  // Register service worker
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', async () => {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');

        console.log('SW registered: ', registration);

        // Handle service worker updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;

          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New content is available
                const notification = app.config.globalProperties.$notify;
                notification.show({
                  type: 'info',
                  message: 'Yeni sürüm mevcut. Sayfayı yenilemek ister misiniz?',
                  persistent: true,
                  actions: [
                    {
                      label: 'Yenile',
                      handler: () => {
                        window.location.reload();
                      },
                    },
                    {
                      label: 'Daha Sonra',
                      handler: () => {
                        // Dismiss notification
                      },
                    },
                  ],
                });
              }
            });
          }
        });
      } catch (error) {
        console.log('SW registration failed: ', error);
      }
    });
  }

  // Handle app install prompt
  let deferredPrompt: any;

  window.addEventListener('beforeinstallprompt', (e) => {
    // Prevent Chrome 67 and earlier from automatically showing the prompt
    e.preventDefault();

    // Stash the event so it can be triggered later
    deferredPrompt = e;

    // Show install button or notification
    const notification = app.config.globalProperties.$notify;
    notification.show({
      type: 'info',
      message: 'Bu uygulamayı cihazınıza yükleyebilirsiniz',
      persistent: true,
      actions: [
        {
          label: 'Yükle',
          handler: async () => {
            if (deferredPrompt) {
              deferredPrompt.prompt();
              const { outcome } = await deferredPrompt.userChoice;
              console.log(`User response to the install prompt: ${outcome}`);
              deferredPrompt = null;
            }
          },
        },
        {
          label: 'Daha Sonra',
          handler: () => {
            // Dismiss notification
          },
        },
      ],
    });
  });

  // Handle successful app install
  window.addEventListener('appinstalled', () => {
    console.log('PWA was installed');

    const notification = app.config.globalProperties.$notify;
    notification.show({
      type: 'positive',
      message: 'Uygulama başarıyla yüklendi!',
      timeout: 3000,
    });
  });
});
```

## Boot Dosyalarının Sırası

```typescript
// quasar.config.ts
export default configure((ctx) => {
  return {
    // ...
    boot: [
      'core', // Core services (must be first)
      'logger', // Logger service
      'api', // API service
      'i18n', // Internationalization
      'auth', // Authentication
      'notification', // Notifications
      'performance', // Performance monitoring
      'pwa', // PWA features (must be last)
    ],
    // ...
  };
});
```

## Boot Utilities

```typescript
// src/boot/utils/boot.utils.ts
export const createBootPlugin = (
  name: string,
  setup: (context: { app: App; router: Router }) => void | Promise<void>,
) => {
  return boot(async ({ app, router }) => {
    try {
      await setup({ app, router });
      console.log(`✅ ${name} boot plugin loaded successfully`);
    } catch (error) {
      console.error(`❌ ${name} boot plugin failed to load:`, error);
      throw error;
    }
  });
};

export const withErrorHandling = (bootFn: (context: any) => void | Promise<void>) => {
  return boot(async (context) => {
    try {
      await bootFn(context);
    } catch (error) {
      console.error('Boot plugin error:', error);

      // Don't throw in production to prevent app crash
      if (EnvConfig.app.environment !== 'production') {
        throw error;
      }
    }
  });
};
```
