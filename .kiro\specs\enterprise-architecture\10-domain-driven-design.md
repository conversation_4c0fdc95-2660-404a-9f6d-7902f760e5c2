# Domain Driven Design (DDD) Yapısı

## Temel DDD Sınıfları

### Base Entity

```typescript
// src/domain/entities/base.entity.ts
export abstract class BaseEntity {
  constructor(
    public readonly id: string,
    public readonly createdAt: Date = new Date(),
    public readonly updatedAt: Date = new Date(),
  ) {}

  abstract validate(): ValidationResult;

  equals(other: BaseEntity): boolean {
    return this.id === other.id;
  }

  clone(): this {
    return Object.create(Object.getPrototypeOf(this), Object.getOwnPropertyDescriptors(this));
  }

  toJSON(): Record<string, any> {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
```

### Base Value Object

```typescript
// src/domain/value-objects/base.value-object.ts
export abstract class BaseValueObject<T> {
  constructor(protected readonly value: T) {
    this.validate();
  }

  abstract validate(): void;

  getValue(): T {
    return this.value;
  }

  equals(other: BaseValueObject<T>): boolean {
    return JSON.stringify(this.value) === JSON.stringify(other.value);
  }

  toString(): string {
    return JSON.stringify(this.value);
  }

  toJSON(): T {
    return this.value;
  }
}
```

### Base Repository Interface

```typescript
// src/domain/repositories/base.repository.ts
export interface BaseRepository<T extends BaseEntity> {
  findById(id: string): Promise<T | null>;
  findAll(criteria?: any): Promise<T[]>;
  save(entity: T): Promise<T>;
  delete(id: string): Promise<void>;
  exists(id: string): Promise<boolean>;
  count(criteria?: any): Promise<number>;
}

export interface PaginatedResult<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface PaginationOptions {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface BaseRepositoryWithPagination<T extends BaseEntity> extends BaseRepository<T> {
  findWithPagination(criteria?: any, options?: PaginationOptions): Promise<PaginatedResult<T>>;
}
```

## Domain Services

```typescript
// src/domain/services/base.domain-service.ts
export abstract class BaseDomainService {
  constructor(
    protected readonly logger: LoggerService,
    protected readonly eventBus: EventBusService,
  ) {}

  protected async publishDomainEvent<T>(eventName: string, data: T): Promise<void> {
    await this.eventBus.emit(`domain.${eventName}`, data);
    this.logger.info(`Domain event published: ${eventName}`, data, this.constructor.name);
  }
}
```

## Domain Events

```typescript
// src/domain/events/base.domain-event.ts
export interface DomainEvent<T = any> {
  id: string;
  aggregateId: string;
  eventType: string;
  eventData: T;
  occurredAt: Date;
  version: number;
}

export abstract class BaseDomainEvent<T = any> implements DomainEvent<T> {
  public readonly id: string;
  public readonly occurredAt: Date;
  public readonly version: number = 1;

  constructor(
    public readonly aggregateId: string,
    public readonly eventType: string,
    public readonly eventData: T,
  ) {
    this.id = `${eventType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.occurredAt = new Date();
  }
}
```

## Aggregate Root

```typescript
// src/domain/entities/aggregate-root.ts
export abstract class AggregateRoot extends BaseEntity {
  private domainEvents: DomainEvent[] = [];

  protected addDomainEvent(event: DomainEvent): void {
    this.domainEvents.push(event);
  }

  public getDomainEvents(): DomainEvent[] {
    return [...this.domainEvents];
  }

  public clearDomainEvents(): void {
    this.domainEvents = [];
  }

  public markEventsAsCommitted(): void {
    this.domainEvents = [];
  }
}
```

## Value Object Örnekleri

### Email Value Object

```typescript
// src/domain/value-objects/email.value-object.ts
export class Email extends BaseValueObject<string> {
  constructor(email: string) {
    super(email.toLowerCase().trim());
  }

  validate(): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(this.value)) {
      throw new Error('Geçersiz email formatı');
    }
  }

  getDomain(): string {
    return this.value.split('@')[1];
  }

  getLocalPart(): string {
    return this.value.split('@')[0];
  }
}
```

### Money Value Object

```typescript
// src/domain/value-objects/money.value-object.ts
export interface MoneyData {
  amount: number;
  currency: string;
}

export class Money extends BaseValueObject<MoneyData> {
  constructor(amount: number, currency: string = 'TRY') {
    super({ amount, currency: currency.toUpperCase() });
  }

  validate(): void {
    if (this.value.amount < 0) {
      throw new Error('Para miktarı negatif olamaz');
    }

    if (!this.value.currency || this.value.currency.length !== 3) {
      throw new Error('Geçersiz para birimi');
    }
  }

  add(other: Money): Money {
    if (this.value.currency !== other.value.currency) {
      throw new Error('Farklı para birimlerini toplayamazsınız');
    }

    return new Money(this.value.amount + other.value.amount, this.value.currency);
  }

  subtract(other: Money): Money {
    if (this.value.currency !== other.value.currency) {
      throw new Error('Farklı para birimlerini çıkaramazsınız');
    }

    return new Money(this.value.amount - other.value.amount, this.value.currency);
  }

  multiply(factor: number): Money {
    return new Money(this.value.amount * factor, this.value.currency);
  }

  isGreaterThan(other: Money): boolean {
    if (this.value.currency !== other.value.currency) {
      throw new Error('Farklı para birimlerini karşılaştıramazsınız');
    }

    return this.value.amount > other.value.amount;
  }

  format(): string {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: this.value.currency,
    }).format(this.value.amount);
  }
}
```

## Domain Entity Örnekleri

### User Entity

```typescript
// src/domain/entities/user.entity.ts
export interface UserData {
  id: string;
  email: Email;
  name: string;
  isActive: boolean;
  roles: string[];
  createdAt: Date;
  updatedAt: Date;
}

export class User extends AggregateRoot {
  constructor(private data: UserData) {
    super(data.id, data.createdAt, data.updatedAt);
  }

  validate(): ValidationResult {
    const errors: ValidationError[] = [];

    if (!this.data.name || this.data.name.trim().length < 2) {
      errors.push({
        field: 'name',
        message: 'İsim en az 2 karakter olmalıdır',
        value: this.data.name,
      });
    }

    if (!this.data.email) {
      errors.push({
        field: 'email',
        message: 'Email gereklidir',
        value: this.data.email,
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Getters
  get email(): Email {
    return this.data.email;
  }

  get name(): string {
    return this.data.name;
  }

  get isActive(): boolean {
    return this.data.isActive;
  }

  get roles(): string[] {
    return [...this.data.roles];
  }

  // Business methods
  activate(): void {
    if (this.data.isActive) {
      throw new Error('Kullanıcı zaten aktif');
    }

    this.data.isActive = true;
    this.data.updatedAt = new Date();

    this.addDomainEvent(
      new UserActivatedEvent(this.id, {
        userId: this.id,
        email: this.data.email.getValue(),
        activatedAt: new Date(),
      }),
    );
  }

  deactivate(): void {
    if (!this.data.isActive) {
      throw new Error('Kullanıcı zaten pasif');
    }

    this.data.isActive = false;
    this.data.updatedAt = new Date();

    this.addDomainEvent(
      new UserDeactivatedEvent(this.id, {
        userId: this.id,
        email: this.data.email.getValue(),
        deactivatedAt: new Date(),
      }),
    );
  }

  addRole(role: string): void {
    if (this.data.roles.includes(role)) {
      throw new Error('Kullanıcı bu role zaten sahip');
    }

    this.data.roles.push(role);
    this.data.updatedAt = new Date();

    this.addDomainEvent(
      new UserRoleAddedEvent(this.id, {
        userId: this.id,
        role,
        addedAt: new Date(),
      }),
    );
  }

  removeRole(role: string): void {
    const index = this.data.roles.indexOf(role);
    if (index === -1) {
      throw new Error('Kullanıcı bu role sahip değil');
    }

    this.data.roles.splice(index, 1);
    this.data.updatedAt = new Date();

    this.addDomainEvent(
      new UserRoleRemovedEvent(this.id, {
        userId: this.id,
        role,
        removedAt: new Date(),
      }),
    );
  }

  hasRole(role: string): boolean {
    return this.data.roles.includes(role);
  }

  toJSON(): Record<string, any> {
    return {
      ...super.toJSON(),
      email: this.data.email.getValue(),
      name: this.data.name,
      isActive: this.data.isActive,
      roles: this.data.roles,
    };
  }
}
```

## Domain Events

```typescript
// src/domain/events/user.events.ts
export class UserActivatedEvent extends BaseDomainEvent<{
  userId: string;
  email: string;
  activatedAt: Date;
}> {
  constructor(aggregateId: string, data: { userId: string; email: string; activatedAt: Date }) {
    super(aggregateId, 'UserActivated', data);
  }
}

export class UserDeactivatedEvent extends BaseDomainEvent<{
  userId: string;
  email: string;
  deactivatedAt: Date;
}> {
  constructor(aggregateId: string, data: { userId: string; email: string; deactivatedAt: Date }) {
    super(aggregateId, 'UserDeactivated', data);
  }
}

export class UserRoleAddedEvent extends BaseDomainEvent<{
  userId: string;
  role: string;
  addedAt: Date;
}> {
  constructor(aggregateId: string, data: { userId: string; role: string; addedAt: Date }) {
    super(aggregateId, 'UserRoleAdded', data);
  }
}

export class UserRoleRemovedEvent extends BaseDomainEvent<{
  userId: string;
  role: string;
  removedAt: Date;
}> {
  constructor(aggregateId: string, data: { userId: string; role: string; removedAt: Date }) {
    super(aggregateId, 'UserRoleRemoved', data);
  }
}
```

## Repository Implementation

```typescript
// src/infrastructure/repositories/user.repository.ts
export class UserRepository implements BaseRepositoryWithPagination<User> {
  constructor(
    private readonly apiService: ApiService,
    private readonly logger: LoggerService,
  ) {}

  async findById(id: string): Promise<User | null> {
    try {
      const response = await this.apiService.get<any>(`/users/${id}`);
      return response ? this.mapToEntity(response) : null;
    } catch (error) {
      this.logger.error('User findById failed', error, 'UserRepository');
      throw error;
    }
  }

  async findAll(criteria?: any): Promise<User[]> {
    try {
      const response = await this.apiService.get<any[]>('/users', { params: criteria });
      return response.map((data) => this.mapToEntity(data));
    } catch (error) {
      this.logger.error('User findAll failed', error, 'UserRepository');
      throw error;
    }
  }

  async findWithPagination(
    criteria?: any,
    options?: PaginationOptions,
  ): Promise<PaginatedResult<User>> {
    try {
      const params = {
        ...criteria,
        page: options?.page || 1,
        pageSize: options?.pageSize || 10,
        sortBy: options?.sortBy,
        sortOrder: options?.sortOrder,
      };

      const response = await this.apiService.get<{
        items: any[];
        total: number;
        page: number;
        pageSize: number;
      }>('/users/paginated', { params });

      return {
        items: response.items.map((data) => this.mapToEntity(data)),
        total: response.total,
        page: response.page,
        pageSize: response.pageSize,
        totalPages: Math.ceil(response.total / response.pageSize),
      };
    } catch (error) {
      this.logger.error('User findWithPagination failed', error, 'UserRepository');
      throw error;
    }
  }

  async save(entity: User): Promise<User> {
    try {
      const data = entity.toJSON();
      const response = entity.id
        ? await this.apiService.put<any>(`/users/${entity.id}`, data)
        : await this.apiService.post<any>('/users', data);

      return this.mapToEntity(response);
    } catch (error) {
      this.logger.error('User save failed', error, 'UserRepository');
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await this.apiService.delete(`/users/${id}`);
    } catch (error) {
      this.logger.error('User delete failed', error, 'UserRepository');
      throw error;
    }
  }

  async exists(id: string): Promise<boolean> {
    try {
      const user = await this.findById(id);
      return user !== null;
    } catch (error) {
      this.logger.error('User exists check failed', error, 'UserRepository');
      return false;
    }
  }

  async count(criteria?: any): Promise<number> {
    try {
      const response = await this.apiService.get<{ count: number }>('/users/count', {
        params: criteria,
      });
      return response.count;
    } catch (error) {
      this.logger.error('User count failed', error, 'UserRepository');
      throw error;
    }
  }

  private mapToEntity(data: any): User {
    return new User({
      id: data.id,
      email: new Email(data.email),
      name: data.name,
      isActive: data.isActive,
      roles: data.roles || [],
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt),
    });
  }
}
```

## Domain Service Örneği

```typescript
// src/domain/services/user.domain-service.ts
export class UserDomainService extends BaseDomainService {
  constructor(
    private readonly userRepository: UserRepository,
    logger: LoggerService,
    eventBus: EventBusService,
  ) {
    super(logger, eventBus);
  }

  async createUser(email: string, name: string): Promise<User> {
    // Business rule: Email must be unique
    const existingUser = await this.findUserByEmail(email);
    if (existingUser) {
      throw new Error('Bu email adresi zaten kullanılıyor');
    }

    const user = new User({
      id: this.generateId(),
      email: new Email(email),
      name: name.trim(),
      isActive: true,
      roles: ['user'],
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    const validationResult = user.validate();
    if (!validationResult.isValid) {
      throw new Error(
        `Validation failed: ${validationResult.errors.map((e) => e.message).join(', ')}`,
      );
    }

    const savedUser = await this.userRepository.save(user);

    // Publish domain events
    const events = savedUser.getDomainEvents();
    for (const event of events) {
      await this.publishDomainEvent(event.eventType, event);
    }
    savedUser.markEventsAsCommitted();

    return savedUser;
  }

  async promoteUserToAdmin(userId: string): Promise<User> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error('Kullanıcı bulunamadı');
    }

    if (!user.isActive) {
      throw new Error('Pasif kullanıcı admin yapılamaz');
    }

    if (user.hasRole('admin')) {
      throw new Error('Kullanıcı zaten admin');
    }

    user.addRole('admin');

    const savedUser = await this.userRepository.save(user);

    // Publish domain events
    const events = savedUser.getDomainEvents();
    for (const event of events) {
      await this.publishDomainEvent(event.eventType, event);
    }
    savedUser.markEventsAsCommitted();

    return savedUser;
  }

  private async findUserByEmail(email: string): Promise<User | null> {
    const users = await this.userRepository.findAll({ email });
    return users.length > 0 ? users[0] : null;
  }

  private generateId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

## DDD Composable

```typescript
// src/core/composables/useDomain.ts
export const useDomain = () => {
  const eventBus = inject<EventBusService>('eventBus');
  const logger = inject<LoggerService>('logger');

  if (!eventBus || !logger) {
    throw new Error('Domain services not available');
  }

  // Domain event subscription
  const subscribeToDomainEvent = <T>(
    eventType: string,
    handler: (event: DomainEvent<T>) => void | Promise<void>,
  ) => {
    return eventBus.on(`domain.${eventType}`, handler);
  };

  // Domain event publishing
  const publishDomainEvent = async <T>(eventType: string, data: T) => {
    await eventBus.emit(`domain.${eventType}`, data);
    logger.info(`Domain event published: ${eventType}`, data, 'DomainComposable');
  };

  return {
    subscribeToDomainEvent,
    publishDomainEvent,
  };
};
```
