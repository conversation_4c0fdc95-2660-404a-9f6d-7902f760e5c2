# <PERSON><PERSON> (Event System)

## Temel Event Bus Arayüzü

### Event Listener ve Unsubscriber

```typescript
// src/core/services/events/event-bus.service.ts
export interface EventListener<T = any> {
  (event: T): void | Promise<void>;
}

export interface EventUnsubscriber {
  (): void;
}

export interface EventBusService {
  emit<T>(eventName: string, data: T): Promise<void>;
  on<T>(eventName: string, listener: EventListener<T>): EventUnsubscriber;
  once<T>(eventName: string, listener: EventListener<T>): EventUnsubscriber;
  off(eventName: string, listener: EventListener): void;
  removeAllListeners(eventName?: string): void;
  listenerCount(eventName: string): number;
}
```

## Event Bus Implementasyonu

### Ana Event Bus Servisi

```typescript
const createEventBusService = (): EventBusService => {
  const listeners = new Map<string, Set<EventListener>>();
  const onceListeners = new Map<string, Set<EventListener>>();

  return {
    async emit<T>(eventName: string, data: T): Promise<void> {
      const regularListeners = listeners.get(eventName) || new Set();
      const onceListenersSet = onceListeners.get(eventName) || new Set();

      // Regular listener'ları çalıştır
      for (const listener of regularListeners) {
        try {
          await listener(data);
        } catch (error) {
          console.error(`Error in event listener for ${eventName}:`, error);
        }
      }

      // Once listener'ları çalıştır ve kaldır
      for (const listener of onceListenersSet) {
        try {
          await listener(data);
        } catch (error) {
          console.error(`Error in once event listener for ${eventName}:`, error);
        }
      }

      // Once listener'ları temizle
      onceListenersSet.clear();
    },

    on<T>(eventName: string, listener: EventListener<T>): EventUnsubscriber {
      if (!listeners.has(eventName)) {
        listeners.set(eventName, new Set());
      }
      listeners.get(eventName)!.add(listener);

      return () => {
        listeners.get(eventName)?.delete(listener);
      };
    },

    once<T>(eventName: string, listener: EventListener<T>): EventUnsubscriber {
      if (!onceListeners.has(eventName)) {
        onceListeners.set(eventName, new Set());
      }
      onceListeners.get(eventName)!.add(listener);

      return () => {
        onceListeners.get(eventName)?.delete(listener);
      };
    },

    off(eventName: string, listener: EventListener): void {
      listeners.get(eventName)?.delete(listener);
      onceListeners.get(eventName)?.delete(listener);
    },

    removeAllListeners(eventName?: string): void {
      if (eventName) {
        listeners.delete(eventName);
        onceListeners.delete(eventName);
      } else {
        listeners.clear();
        onceListeners.clear();
      }
    },

    listenerCount(eventName: string): number {
      const regular = listeners.get(eventName)?.size || 0;
      const once = onceListeners.get(eventName)?.size || 0;
      return regular + once;
    },
  };
};
```

## Gelişmiş Event Dispatcher

### Event Dispatcher Servisi

```typescript
// src/core/services/events/event-dispatcher.service.ts
export interface EventDispatcher {
  dispatch<T>(event: DomainEvent<T>): Promise<void>;
  subscribe<T>(eventType: string, handler: EventHandler<T>): EventUnsubscriber;
  subscribeToPattern(pattern: RegExp, handler: EventHandler<any>): EventUnsubscriber;
}

export interface DomainEvent<T = any> {
  id: string;
  type: string;
  data: T;
  timestamp: Date;
  source?: string;
  version?: number;
  metadata?: Record<string, any>;
}

export interface EventHandler<T = any> {
  handle(event: DomainEvent<T>): Promise<void>;
}

const createEventDispatcher = (eventBus: EventBusService): EventDispatcher => {
  const patternSubscriptions = new Map<RegExp, EventHandler<any>[]>();

  return {
    async dispatch<T>(event: DomainEvent<T>): Promise<void> {
      // Normal event listener'lara gönder
      await eventBus.emit(event.type, event);

      // Pattern-based subscription'lara gönder
      for (const [pattern, handlers] of patternSubscriptions.entries()) {
        if (pattern.test(event.type)) {
          for (const handler of handlers) {
            try {
              await handler.handle(event);
            } catch (error) {
              console.error(`Error in pattern handler for ${event.type}:`, error);
            }
          }
        }
      }
    },

    subscribe<T>(eventType: string, handler: EventHandler<T>): EventUnsubscriber {
      return eventBus.on(eventType, (event: DomainEvent<T>) => handler.handle(event));
    },

    subscribeToPattern(pattern: RegExp, handler: EventHandler<any>): EventUnsubscriber {
      if (!patternSubscriptions.has(pattern)) {
        patternSubscriptions.set(pattern, []);
      }
      patternSubscriptions.get(pattern)!.push(handler);

      return () => {
        const handlers = patternSubscriptions.get(pattern);
        if (handlers) {
          const index = handlers.indexOf(handler);
          if (index > -1) {
            handlers.splice(index, 1);
          }
          if (handlers.length === 0) {
            patternSubscriptions.delete(pattern);
          }
        }
      };
    },
  };
};
```

## Domain Events

### Event Factory

```typescript
// src/core/services/events/event.factory.ts
export class EventFactory {
  static create<T>(
    type: string,
    data: T,
    source?: string,
    metadata?: Record<string, any>,
  ): DomainEvent<T> {
    return {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: new Date(),
      source,
      version: 1,
      metadata,
    };
  }

  static createUserEvent<T>(
    type: string,
    data: T,
    userId: string,
    metadata?: Record<string, any>,
  ): DomainEvent<T> {
    return this.create(type, data, 'user-service', {
      userId,
      ...metadata,
    });
  }

  static createSystemEvent<T>(
    type: string,
    data: T,
    component: string,
    metadata?: Record<string, any>,
  ): DomainEvent<T> {
    return this.create(type, data, component, {
      system: true,
      ...metadata,
    });
  }
}
```

### Tiplendirilmiş Event'ler

```typescript
// src/core/types/event.types.ts
export interface UserCreatedEvent {
  userId: string;
  email: string;
  name: string;
  createdAt: Date;
}

export interface UserUpdatedEvent {
  userId: string;
  changes: Partial<User>;
  updatedAt: Date;
}

export interface OrderPlacedEvent {
  orderId: string;
  userId: string;
  items: OrderItem[];
  total: number;
  placedAt: Date;
}

export interface PaymentProcessedEvent {
  paymentId: string;
  orderId: string;
  amount: number;
  status: 'success' | 'failed';
  processedAt: Date;
}

// Event type registry
export const EventTypes = {
  USER_CREATED: 'user.created',
  USER_UPDATED: 'user.updated',
  USER_DELETED: 'user.deleted',
  ORDER_PLACED: 'order.placed',
  ORDER_CANCELLED: 'order.cancelled',
  PAYMENT_PROCESSED: 'payment.processed',
  NOTIFICATION_SENT: 'notification.sent',
} as const;
```

## Event Handlers

### Base Event Handler

```typescript
// src/core/services/events/base-event-handler.ts
export abstract class BaseEventHandler<T> implements EventHandler<T> {
  abstract handle(event: DomainEvent<T>): Promise<void>;

  protected async withRetry<R>(
    operation: () => Promise<R>,
    maxRetries: number = 3,
    delay: number = 1000,
  ): Promise<R> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        if (attempt < maxRetries) {
          await new Promise((resolve) => setTimeout(resolve, delay * attempt));
        }
      }
    }

    throw lastError!;
  }

  protected log(message: string, data?: any): void {
    console.log(`[${this.constructor.name}] ${message}`, data);
  }
}
```

### Spesifik Event Handler'lar

```typescript
// src/modules/user/handlers/user-created.handler.ts
export class UserCreatedHandler extends BaseEventHandler<UserCreatedEvent> {
  constructor(
    private emailService: EmailService,
    private analyticsService: AnalyticsService,
  ) {
    super();
  }

  async handle(event: DomainEvent<UserCreatedEvent>): Promise<void> {
    this.log('Handling user created event', event.data);

    await this.withRetry(async () => {
      // Hoş geldin e-postası gönder
      await this.emailService.sendWelcomeEmail(event.data.email, event.data.name);

      // Analytics'e kaydet
      await this.analyticsService.track('user_registered', {
        userId: event.data.userId,
        timestamp: event.timestamp,
      });
    });
  }
}

// src/modules/order/handlers/order-placed.handler.ts
export class OrderPlacedHandler extends BaseEventHandler<OrderPlacedEvent> {
  constructor(
    private inventoryService: InventoryService,
    private notificationService: NotificationService,
  ) {
    super();
  }

  async handle(event: DomainEvent<OrderPlacedEvent>): Promise<void> {
    this.log('Handling order placed event', event.data);

    // Stok güncelle
    await this.inventoryService.reserveItems(event.data.items);

    // Kullanıcıya bildirim gönder
    await this.notificationService.send({
      userId: event.data.userId,
      type: 'order_confirmation',
      data: {
        orderId: event.data.orderId,
        total: event.data.total,
      },
    });
  }
}
```

## Event Store

### Event Persistence

```typescript
// src/core/services/events/event-store.service.ts
export interface EventStore {
  append(event: DomainEvent): Promise<void>;
  getEvents(streamId: string, fromVersion?: number): Promise<DomainEvent[]>;
  getEventsByType(eventType: string, limit?: number): Promise<DomainEvent[]>;
  getEventsAfter(timestamp: Date): Promise<DomainEvent[]>;
}

export class InMemoryEventStore implements EventStore {
  private events: DomainEvent[] = [];
  private eventsByStream = new Map<string, DomainEvent[]>();
  private eventsByType = new Map<string, DomainEvent[]>();

  async append(event: DomainEvent): Promise<void> {
    this.events.push(event);

    // Stream'e göre indexle
    if (event.source) {
      if (!this.eventsByStream.has(event.source)) {
        this.eventsByStream.set(event.source, []);
      }
      this.eventsByStream.get(event.source)!.push(event);
    }

    // Type'a göre indexle
    if (!this.eventsByType.has(event.type)) {
      this.eventsByType.set(event.type, []);
    }
    this.eventsByType.get(event.type)!.push(event);
  }

  async getEvents(streamId: string, fromVersion?: number): Promise<DomainEvent[]> {
    const streamEvents = this.eventsByStream.get(streamId) || [];

    if (fromVersion !== undefined) {
      return streamEvents.filter((event) => (event.version || 1) >= fromVersion);
    }

    return streamEvents;
  }

  async getEventsByType(eventType: string, limit?: number): Promise<DomainEvent[]> {
    const typeEvents = this.eventsByType.get(eventType) || [];

    if (limit) {
      return typeEvents.slice(-limit);
    }

    return typeEvents;
  }

  async getEventsAfter(timestamp: Date): Promise<DomainEvent[]> {
    return this.events.filter((event) => event.timestamp > timestamp);
  }
}
```

## Event Sourcing

### Aggregate Root

```typescript
// src/domain/entities/aggregate-root.ts
export abstract class AggregateRoot {
  private _uncommittedEvents: DomainEvent[] = [];
  private _version = 0;

  protected addEvent(event: DomainEvent): void {
    this._uncommittedEvents.push(event);
    this._version++;
  }

  getUncommittedEvents(): DomainEvent[] {
    return [...this._uncommittedEvents];
  }

  clearUncommittedEvents(): void {
    this._uncommittedEvents = [];
  }

  getVersion(): number {
    return this._version;
  }

  protected applyEvent(event: DomainEvent): void {
    const handler = this.getEventHandler(event.type);
    if (handler) {
      handler.call(this, event.data);
    }
  }

  private getEventHandler(eventType: string): Function | undefined {
    const methodName = `on${eventType.replace(/\./g, '')}`;
    return (this as any)[methodName];
  }

  static fromHistory(events: DomainEvent[]): AggregateRoot {
    const instance = new (this as any)();

    events.forEach((event) => {
      instance.applyEvent(event);
      instance._version++;
    });

    return instance;
  }
}
```

### User Aggregate Örneği

```typescript
// src/domain/entities/user.aggregate.ts
export class UserAggregate extends AggregateRoot {
  private id: string = '';
  private email: string = '';
  private name: string = '';
  private isActive: boolean = true;

  static create(id: string, email: string, name: string): UserAggregate {
    const user = new UserAggregate();

    const event = EventFactory.create(EventTypes.USER_CREATED, {
      userId: id,
      email,
      name,
      createdAt: new Date(),
    });

    user.addEvent(event);
    user.applyEvent(event);

    return user;
  }

  updateProfile(name: string): void {
    if (name !== this.name) {
      const event = EventFactory.create(EventTypes.USER_UPDATED, {
        userId: this.id,
        changes: { name },
        updatedAt: new Date(),
      });

      this.addEvent(event);
      this.applyEvent(event);
    }
  }

  deactivate(): void {
    if (this.isActive) {
      const event = EventFactory.create(EventTypes.USER_DELETED, {
        userId: this.id,
        deletedAt: new Date(),
      });

      this.addEvent(event);
      this.applyEvent(event);
    }
  }

  // Event handlers
  private onUserCreated(data: UserCreatedEvent): void {
    this.id = data.userId;
    this.email = data.email;
    this.name = data.name;
  }

  private onUserUpdated(data: UserUpdatedEvent): void {
    if (data.changes.name) {
      this.name = data.changes.name;
    }
  }

  private onUserDeleted(): void {
    this.isActive = false;
  }

  // Getters
  getId(): string {
    return this.id;
  }
  getEmail(): string {
    return this.email;
  }
  getName(): string {
    return this.name;
  }
  getIsActive(): boolean {
    return this.isActive;
  }
}
```

## Event Composable

### Vue Event Composable

```typescript
// src/core/composables/useEvents.ts
export const useEvents = () => {
  const eventBus = inject<EventBusService>('eventBus');
  const eventDispatcher = inject<EventDispatcher>('eventDispatcher');

  if (!eventBus || !eventDispatcher) {
    throw new Error('Event services not found');
  }

  const emit = async <T>(eventName: string, data: T) => {
    await eventBus.emit(eventName, data);
  };

  const dispatch = async <T>(event: DomainEvent<T>) => {
    await eventDispatcher.dispatch(event);
  };

  const on = <T>(eventName: string, listener: EventListener<T>) => {
    return eventBus.on(eventName, listener);
  };

  const once = <T>(eventName: string, listener: EventListener<T>) => {
    return eventBus.once(eventName, listener);
  };

  const subscribe = <T>(eventType: string, handler: EventHandler<T>) => {
    return eventDispatcher.subscribe(eventType, handler);
  };

  // Reactive event listener
  const useEventListener = <T>(eventName: string, initialValue?: T) => {
    const data = ref<T | undefined>(initialValue);
    const lastEvent = ref<DomainEvent<T> | null>(null);

    const unsubscribe = on<T>(eventName, (event) => {
      if (typeof event === 'object' && 'data' in event) {
        data.value = event.data;
        lastEvent.value = event as DomainEvent<T>;
      } else {
        data.value = event;
      }
    });

    onUnmounted(() => {
      unsubscribe();
    });

    return {
      data: readonly(data),
      lastEvent: readonly(lastEvent),
    };
  };

  return {
    emit,
    dispatch,
    on,
    once,
    subscribe,
    useEventListener,
  };
};
```

## Event Middleware

### Event Processing Pipeline

```typescript
// src/core/services/events/event-middleware.ts
export interface EventMiddleware {
  process<T>(event: DomainEvent<T>, next: () => Promise<void>): Promise<void>;
}

export class LoggingMiddleware implements EventMiddleware {
  constructor(private logger: LoggerService) {}

  async process<T>(event: DomainEvent<T>, next: () => Promise<void>): Promise<void> {
    this.logger.info(`Processing event: ${event.type}`, event, 'EventMiddleware');

    const startTime = Date.now();
    await next();
    const duration = Date.now() - startTime;

    this.logger.info(`Event processed in ${duration}ms`, { eventId: event.id }, 'EventMiddleware');
  }
}

export class ValidationMiddleware implements EventMiddleware {
  async process<T>(event: DomainEvent<T>, next: () => Promise<void>): Promise<void> {
    // Event validation
    if (!event.id || !event.type || !event.timestamp) {
      throw new Error('Invalid event structure');
    }

    await next();
  }
}

export class EventPipeline {
  private middlewares: EventMiddleware[] = [];

  use(middleware: EventMiddleware): void {
    this.middlewares.push(middleware);
  }

  async process<T>(event: DomainEvent<T>, handler: () => Promise<void>): Promise<void> {
    let index = 0;

    const next = async (): Promise<void> => {
      if (index < this.middlewares.length) {
        const middleware = this.middlewares[index++];
        await middleware.process(event, next);
      } else {
        await handler();
      }
    };

    await next();
  }
}
```

Bu olay sistemi, uygulamanın farklı bölümleri arasında gevşek bağlantı sağlar ve event-driven architecture desenlerini destekler. Sistem ayrıca event sourcing ve CQRS desenlerini de destekleyecek şekilde tasarlanmıştır.
