<template>
  <q-btn
    flat
    dense
    round
    :icon="themeStore.isDark ? 'dark_mode' : 'light_mode'"
    :aria-label="themeStore.isDark ? t('lightTheme') : t('darkTheme')"
    @click="themeStore.toggleTheme"
  >
    <q-tooltip>
      {{ themeStore.isDark ? t('toggleLightTheme') : t('toggleDarkTheme') }}
    </q-tooltip>
  </q-btn>
</template>

<script setup lang="ts">
import { useThemeStore } from 'src/stores/theme-store';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

/**
 * ThemeToggle, kullanıcıların açık ve koyu tema arasında geçiş yapmasını
 * sağlayan basit bir arayüz bileşenidir (Dumb Component).
 *
 * Tüm mantık ve durum yönetimi `useThemeStore`'dan gelir.
 */
const themeStore = useThemeStore();
</script>
