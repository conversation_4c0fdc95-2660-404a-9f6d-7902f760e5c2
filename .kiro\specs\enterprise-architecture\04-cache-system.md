# Gelişmiş Önbellek Sistemi

## Önbellek Arayüzleri

### Temel Önbellek Arayüzü

```typescript
// src/core/services/cache/cache.factory.ts
export interface CacheEntry<T = any> {
  value: T;
  timestamp: number;
  ttl: number;
  accessed: number;
  hits: number;
}

export interface CacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  has(key: string): Promise<boolean>;
  size(): Promise<number>;
  keys(): Promise<string[]>;
  stats(): Promise<CacheStats>;
}

export interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  memoryUsage: number;
}
```

### Önbellek Factory

```typescript
const createCacheService = (config: CacheConfig): CacheService => {
  switch (config.provider) {
    case 'memory':
      return createMemoryCache(config);
    case 'localStorage':
      return createLocalStorageCache(config);
    case 'redis':
      return createRedisCache(config);
    case 'hybrid':
      return createHybridCache(config);
    default:
      throw new Error(`Unsupported cache provider: ${config.provider}`);
  }
};
```

## Memory Cache Implementasyonu

### Bellek Önbelleği

```typescript
// src/core/services/cache/memory.cache.ts
const createMemoryCache = (config: CacheConfig): CacheService => {
  const cache = new Map<string, CacheEntry>();
  let stats: CacheStats = { hits: 0, misses: 0, size: 0, memoryUsage: 0 };

  // Otomatik temizlik
  const cleanup = () => {
    const now = Date.now();
    for (const [key, entry] of cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        cache.delete(key);
      }
    }
  };

  // Her dakika temizlik yap
  setInterval(cleanup, 60000);

  const isExpired = (entry: CacheEntry): boolean => {
    return Date.now() - entry.timestamp > entry.ttl;
  };

  return {
    async get<T>(key: string): Promise<T | null> {
      const entry = cache.get(key);

      if (!entry || isExpired(entry)) {
        stats.misses++;
        if (entry) cache.delete(key);
        return null;
      }

      entry.accessed = Date.now();
      entry.hits++;
      stats.hits++;
      return entry.value;
    },

    async set<T>(key: string, value: T, ttl?: number): Promise<void> {
      const entry: CacheEntry<T> = {
        value,
        timestamp: Date.now(),
        ttl: ttl || config.ttl,
        accessed: Date.now(),
        hits: 0,
      };

      // Boyut limitini kontrol et
      if (cache.size >= config.maxSize) {
        // LRU entry'yi kaldır
        const lruKey = Array.from(cache.entries()).sort(
          ([, a], [, b]) => a.accessed - b.accessed,
        )[0][0];
        cache.delete(lruKey);
      }

      cache.set(key, entry);
      stats.size = cache.size;
    },

    async delete(key: string): Promise<void> {
      cache.delete(key);
      stats.size = cache.size;
    },

    async clear(): Promise<void> {
      cache.clear();
      stats = { hits: 0, misses: 0, size: 0, memoryUsage: 0 };
    },

    async has(key: string): Promise<boolean> {
      const entry = cache.get(key);
      return entry !== undefined && !isExpired(entry);
    },

    async size(): Promise<number> {
      return cache.size;
    },

    async keys(): Promise<string[]> {
      return Array.from(cache.keys());
    },

    async stats(): Promise<CacheStats> {
      return { ...stats };
    },
  };
};
```

## LocalStorage Cache Implementasyonu

### LocalStorage Önbelleği

```typescript
// src/core/services/cache/local-storage.cache.ts
const createLocalStorageCache = (config: CacheConfig): CacheService => {
  const prefix = 'cache_';
  let stats: CacheStats = { hits: 0, misses: 0, size: 0, memoryUsage: 0 };

  const getStorageKey = (key: string): string => `${prefix}${key}`;

  const isExpired = (entry: CacheEntry): boolean => {
    return Date.now() - entry.timestamp > entry.ttl;
  };

  const cleanup = () => {
    const keysToRemove: string[] = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(prefix)) {
        try {
          const entry: CacheEntry = JSON.parse(localStorage.getItem(key)!);
          if (isExpired(entry)) {
            keysToRemove.push(key);
          }
        } catch {
          keysToRemove.push(key);
        }
      }
    }

    keysToRemove.forEach((key) => localStorage.removeItem(key));
  };

  // Periyodik temizlik
  setInterval(cleanup, 300000); // 5 dakika

  return {
    async get<T>(key: string): Promise<T | null> {
      try {
        const storageKey = getStorageKey(key);
        const item = localStorage.getItem(storageKey);

        if (!item) {
          stats.misses++;
          return null;
        }

        const entry: CacheEntry<T> = JSON.parse(item);

        if (isExpired(entry)) {
          localStorage.removeItem(storageKey);
          stats.misses++;
          return null;
        }

        entry.accessed = Date.now();
        entry.hits++;
        localStorage.setItem(storageKey, JSON.stringify(entry));
        stats.hits++;

        return entry.value;
      } catch {
        stats.misses++;
        return null;
      }
    },

    async set<T>(key: string, value: T, ttl?: number): Promise<void> {
      try {
        const entry: CacheEntry<T> = {
          value,
          timestamp: Date.now(),
          ttl: ttl || config.ttl,
          accessed: Date.now(),
          hits: 0,
        };

        const storageKey = getStorageKey(key);

        // Sıkıştırma varsa uygula
        let serializedEntry = JSON.stringify(entry);
        if (config.compression) {
          // Basit sıkıştırma implementasyonu
          serializedEntry = btoa(serializedEntry);
        }

        localStorage.setItem(storageKey, serializedEntry);
        stats.size++;
      } catch (error) {
        // Storage dolu olabilir, eski girişleri temizle
        cleanup();
        throw error;
      }
    },

    async delete(key: string): Promise<void> {
      const storageKey = getStorageKey(key);
      localStorage.removeItem(storageKey);
      stats.size = Math.max(0, stats.size - 1);
    },

    async clear(): Promise<void> {
      const keysToRemove: string[] = [];

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(prefix)) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach((key) => localStorage.removeItem(key));
      stats = { hits: 0, misses: 0, size: 0, memoryUsage: 0 };
    },

    async has(key: string): Promise<boolean> {
      try {
        const storageKey = getStorageKey(key);
        const item = localStorage.getItem(storageKey);

        if (!item) return false;

        const entry: CacheEntry = JSON.parse(item);
        return !isExpired(entry);
      } catch {
        return false;
      }
    },

    async size(): Promise<number> {
      let count = 0;
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(prefix)) {
          count++;
        }
      }
      return count;
    },

    async keys(): Promise<string[]> {
      const keys: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(prefix)) {
          keys.push(key.substring(prefix.length));
        }
      }
      return keys;
    },

    async stats(): Promise<CacheStats> {
      return { ...stats };
    },
  };
};
```

## Hibrit Cache Implementasyonu

### Çok Katmanlı Önbellek

```typescript
// src/core/services/cache/hybrid.cache.ts
const createHybridCache = (config: CacheConfig): CacheService => {
  const l1Cache = createMemoryCache({ ...config, maxSize: Math.floor(config.maxSize * 0.3) });
  const l2Cache = createLocalStorageCache({ ...config, maxSize: Math.floor(config.maxSize * 0.7) });

  return {
    async get<T>(key: string): Promise<T | null> {
      // Önce L1 cache'e bak
      let value = await l1Cache.get<T>(key);

      if (value !== null) {
        return value;
      }

      // L2 cache'e bak
      value = await l2Cache.get<T>(key);

      if (value !== null) {
        // L1 cache'e de ekle (promotion)
        await l1Cache.set(key, value);
        return value;
      }

      return null;
    },

    async set<T>(key: string, value: T, ttl?: number): Promise<void> {
      // Her iki cache'e de ekle
      await Promise.all([l1Cache.set(key, value, ttl), l2Cache.set(key, value, ttl)]);
    },

    async delete(key: string): Promise<void> {
      await Promise.all([l1Cache.delete(key), l2Cache.delete(key)]);
    },

    async clear(): Promise<void> {
      await Promise.all([l1Cache.clear(), l2Cache.clear()]);
    },

    async has(key: string): Promise<boolean> {
      const hasInL1 = await l1Cache.has(key);
      if (hasInL1) return true;

      return await l2Cache.has(key);
    },

    async size(): Promise<number> {
      const [l1Size, l2Size] = await Promise.all([l1Cache.size(), l2Cache.size()]);
      return l1Size + l2Size;
    },

    async keys(): Promise<string[]> {
      const [l1Keys, l2Keys] = await Promise.all([l1Cache.keys(), l2Cache.keys()]);
      return [...new Set([...l1Keys, ...l2Keys])];
    },

    async stats(): Promise<CacheStats> {
      const [l1Stats, l2Stats] = await Promise.all([l1Cache.stats(), l2Cache.stats()]);

      return {
        hits: l1Stats.hits + l2Stats.hits,
        misses: l1Stats.misses + l2Stats.misses,
        size: l1Stats.size + l2Stats.size,
        memoryUsage: l1Stats.memoryUsage + l2Stats.memoryUsage,
      };
    },
  };
};
```

## Cache Decorator

### Otomatik Önbellekleme

```typescript
// src/core/decorators/cache.decorator.ts
export function Cached(ttl?: number, keyGenerator?: (...args: any[]) => string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const cache = new Map<string, { value: any; timestamp: number; ttl: number }>();

    descriptor.value = async function (...args: any[]) {
      const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
      const now = Date.now();
      const cached = cache.get(key);

      if (cached && now - cached.timestamp < cached.ttl) {
        return cached.value;
      }

      const result = await method.apply(this, args);
      cache.set(key, {
        value: result,
        timestamp: now,
        ttl: ttl || 300000, // 5 dakika default
      });

      return result;
    };

    return descriptor;
  };
}

// Kullanım örneği:
class UserService {
  @Cached(600000, (userId: string) => `user_${userId}`)
  async getUserById(userId: string): Promise<User> {
    // API çağrısı
    return await this.api.get(`/users/${userId}`);
  }
}
```

## Cache Composable

### Vue Composable

```typescript
// src/core/composables/useCache.ts
export const useCache = () => {
  const cache = inject<CacheService>('cache');

  if (!cache) {
    throw new Error('Cache service not found');
  }

  const cachedRef = <T>(key: string, fetcher: () => Promise<T>, ttl?: number) => {
    const data = ref<T | null>(null);
    const loading = ref(false);
    const error = ref<Error | null>(null);

    const fetch = async () => {
      loading.value = true;
      error.value = null;

      try {
        // Önce cache'e bak
        const cached = await cache.get<T>(key);
        if (cached !== null) {
          data.value = cached;
          loading.value = false;
          return cached;
        }

        // Cache'de yoksa fetch et
        const result = await fetcher();
        await cache.set(key, result, ttl);
        data.value = result;
        return result;
      } catch (err) {
        error.value = err as Error;
        throw err;
      } finally {
        loading.value = false;
      }
    };

    const invalidate = async () => {
      await cache.delete(key);
      data.value = null;
    };

    const refresh = async () => {
      await invalidate();
      return await fetch();
    };

    return {
      data: readonly(data),
      loading: readonly(loading),
      error: readonly(error),
      fetch,
      invalidate,
      refresh,
    };
  };

  return {
    cache,
    cachedRef,
  };
};
```

## Cache Stratejileri

### LRU (Least Recently Used)

```typescript
// src/core/services/cache/strategies/lru.strategy.ts
export class LRUCache<T> {
  private cache = new Map<string, { value: T; timestamp: number }>();
  private maxSize: number;

  constructor(maxSize: number) {
    this.maxSize = maxSize;
  }

  get(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    // Move to end (most recently used)
    this.cache.delete(key);
    this.cache.set(key, { ...item, timestamp: Date.now() });

    return item.value;
  }

  set(key: string, value: T): void {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.maxSize) {
      // Remove least recently used (first item)
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, { value, timestamp: Date.now() });
  }
}
```

### TTL (Time To Live)

```typescript
// src/core/services/cache/strategies/ttl.strategy.ts
export class TTLCache<T> {
  private cache = new Map<string, { value: T; expiry: number }>();

  set(key: string, value: T, ttl: number): void {
    const expiry = Date.now() + ttl;
    this.cache.set(key, { value, expiry });
  }

  get(key: string): T | null {
    const item = this.cache.get(key);

    if (!item) return null;

    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.value;
  }

  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }
}
```

Bu önbellek sistemi, farklı kullanım senaryoları için esnek ve performanslı çözümler sunar. Memory cache hızlı erişim için, localStorage cache tarayıcı oturumları arası kalıcılık için, hibrit cache ise her ikisinin avantajlarını birleştirmek için kullanılabilir.
