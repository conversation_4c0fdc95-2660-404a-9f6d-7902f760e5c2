<template>
  <q-header elevated>
    <q-toolbar>
      <q-btn flat dense icon="menu" aria-label="Menu" @click="layoutStore.toggleLeftDrawer" />

      <q-toolbar-title> {{ $t('appName') }}</q-toolbar-title>

      <div class="row q-gutter-x-sm">
        <theme-toggle />
        <language-toggle />
      </div>
    </q-toolbar>
  </q-header>
</template>

<script setup lang="ts">
import { useLayoutStore } from 'src/stores/layout-store';
import ThemeToggle from 'src/components/theme/ThemeToggle.vue';
import LanguageToggle from 'src/components/language/LanguageToggle.vue';

/**
 * TheHeader bileşeni, uygulamanın üst bilgi çubuğunu temsil eder.
 * Sorumluluğu sadece görsel arayüzü sunmaktır (Dumb Component).
 *
 * @see https://vuejs.org/guide/scaling-up/sfc.html
 * @see https://quasar.dev/layout/header-and-footer
 */

// Layout durumunu yönetmek için merkezi store'u kullanıyoruz.
// Bu, bileşenin kendi içinde durum tutmasını engelleyerek SRP'ye uymasını sağlar.
const layoutStore = useLayoutStore();
</script>
