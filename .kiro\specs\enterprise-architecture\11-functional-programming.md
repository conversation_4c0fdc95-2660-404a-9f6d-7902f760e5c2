# Fonksiyonel Programlama Desenleri

## Maybe Monad

```typescript
// src/core/utils/fp-patterns.ts
export interface Maybe<T> {
  map<U>(fn: (value: T) => U): Maybe<U>;
  flatMap<U>(fn: (value: T) => Maybe<U>): Maybe<U>;
  filter(predicate: (value: T) => boolean): Maybe<T>;
  getOrElse(defaultValue: T): T;
  isSome(): boolean;
  isNone(): boolean;
  fold<U>(noneValue: U, someValue: (value: T) => U): U;
}

class Some<T> implements Maybe<T> {
  constructor(private readonly value: T) {}

  map<U>(fn: (value: T) => U): Maybe<U> {
    try {
      return Maybe.of(fn(this.value));
    } catch {
      return Maybe.none<U>();
    }
  }

  flatMap<U>(fn: (value: T) => Maybe<U>): Maybe<U> {
    try {
      return fn(this.value);
    } catch {
      return Maybe.none<U>();
    }
  }

  filter(predicate: (value: T) => boolean): Maybe<T> {
    try {
      return predicate(this.value) ? this : Maybe.none<T>();
    } catch {
      return Maybe.none<T>();
    }
  }

  getOrElse(_defaultValue: T): T {
    return this.value;
  }

  isSome(): boolean {
    return true;
  }

  isNone(): boolean {
    return false;
  }

  fold<U>(_noneValue: U, someValue: (value: T) => U): U {
    return someValue(this.value);
  }
}

class None<T> implements Maybe<T> {
  map<U>(_fn: (value: T) => U): Maybe<U> {
    return Maybe.none<U>();
  }

  flatMap<U>(_fn: (value: T) => Maybe<U>): Maybe<U> {
    return Maybe.none<U>();
  }

  filter(_predicate: (value: T) => boolean): Maybe<T> {
    return Maybe.none<T>();
  }

  getOrElse(defaultValue: T): T {
    return defaultValue;
  }

  isSome(): boolean {
    return false;
  }

  isNone(): boolean {
    return true;
  }

  fold<U>(noneValue: U, _someValue: (value: T) => U): U {
    return noneValue;
  }
}

export const Maybe = {
  of: <T>(value: T | null | undefined): Maybe<T> => {
    return value != null ? new Some(value) : new None<T>();
  },

  some: <T>(value: T): Maybe<T> => new Some(value),

  none: <T>(): Maybe<T> => new None<T>(),

  fromNullable: <T>(value: T | null | undefined): Maybe<T> => {
    return Maybe.of(value);
  },

  fromPredicate: <T>(value: T, predicate: (value: T) => boolean): Maybe<T> => {
    return predicate(value) ? Maybe.some(value) : Maybe.none<T>();
  },
};
```

## Either Monad

```typescript
// src/core/utils/fp-patterns.ts
export interface Either<L, R> {
  map<U>(fn: (value: R) => U): Either<L, U>;
  flatMap<U>(fn: (value: R) => Either<L, U>): Either<L, U>;
  mapLeft<U>(fn: (error: L) => U): Either<U, R>;
  fold<U>(leftFn: (error: L) => U, rightFn: (value: R) => U): U;
  isRight(): boolean;
  isLeft(): boolean;
  getOrElse(defaultValue: R): R;
  swap(): Either<R, L>;
}

class Right<L, R> implements Either<L, R> {
  constructor(private readonly value: R) {}

  map<U>(fn: (value: R) => U): Either<L, U> {
    try {
      return Either.right<L, U>(fn(this.value));
    } catch (error) {
      return Either.left<L, U>(error as L);
    }
  }

  flatMap<U>(fn: (value: R) => Either<L, U>): Either<L, U> {
    try {
      return fn(this.value);
    } catch (error) {
      return Either.left<L, U>(error as L);
    }
  }

  mapLeft<U>(_fn: (error: L) => U): Either<U, R> {
    return Either.right<U, R>(this.value);
  }

  fold<U>(_leftFn: (error: L) => U, rightFn: (value: R) => U): U {
    return rightFn(this.value);
  }

  isRight(): boolean {
    return true;
  }

  isLeft(): boolean {
    return false;
  }

  getOrElse(_defaultValue: R): R {
    return this.value;
  }

  swap(): Either<R, L> {
    return Either.left<R, L>(this.value);
  }
}

class Left<L, R> implements Either<L, R> {
  constructor(private readonly error: L) {}

  map<U>(_fn: (value: R) => U): Either<L, U> {
    return Either.left<L, U>(this.error);
  }

  flatMap<U>(_fn: (value: R) => Either<L, U>): Either<L, U> {
    return Either.left<L, U>(this.error);
  }

  mapLeft<U>(fn: (error: L) => U): Either<U, R> {
    return Either.left<U, R>(fn(this.error));
  }

  fold<U>(leftFn: (error: L) => U, _rightFn: (value: R) => U): U {
    return leftFn(this.error);
  }

  isRight(): boolean {
    return false;
  }

  isLeft(): boolean {
    return true;
  }

  getOrElse(defaultValue: R): R {
    return defaultValue;
  }

  swap(): Either<R, L> {
    return Either.right<R, L>(this.error);
  }
}

export const Either = {
  right: <L, R>(value: R): Either<L, R> => new Right<L, R>(value),

  left: <L, R>(error: L): Either<L, R> => new Left<L, R>(error),

  fromNullable: <L, R>(value: R | null | undefined, error: L): Either<L, R> => {
    return value != null ? Either.right<L, R>(value) : Either.left<L, R>(error);
  },

  fromPredicate: <L, R>(value: R, predicate: (value: R) => boolean, error: L): Either<L, R> => {
    return predicate(value) ? Either.right<L, R>(value) : Either.left<L, R>(error);
  },

  tryCatch: <L, R>(fn: () => R, onError: (error: unknown) => L): Either<L, R> => {
    try {
      return Either.right<L, R>(fn());
    } catch (error) {
      return Either.left<L, R>(onError(error));
    }
  },
};
```

## Task Monad (Async Operations)

```typescript
// src/core/utils/fp-patterns.ts
export interface Task<T> {
  map<U>(fn: (value: T) => U): Task<U>;
  flatMap<U>(fn: (value: T) => Task<U>): Task<U>;
  run(): Promise<T>;
  timeout(ms: number): Task<T>;
  retry(attempts: number, delay?: number): Task<T>;
}

class TaskImpl<T> implements Task<T> {
  constructor(private readonly computation: () => Promise<T>) {}

  map<U>(fn: (value: T) => U): Task<U> {
    return new TaskImpl(async () => {
      const value = await this.computation();
      return fn(value);
    });
  }

  flatMap<U>(fn: (value: T) => Task<U>): Task<U> {
    return new TaskImpl(async () => {
      const value = await this.computation();
      const nextTask = fn(value);
      return await nextTask.run();
    });
  }

  run(): Promise<T> {
    return this.computation();
  }

  timeout(ms: number): Task<T> {
    return new TaskImpl(() => {
      return Promise.race([
        this.computation(),
        new Promise<T>((_, reject) =>
          setTimeout(() => reject(new Error(`Task timed out after ${ms}ms`)), ms),
        ),
      ]);
    });
  }

  retry(attempts: number, delay: number = 1000): Task<T> {
    return new TaskImpl(async () => {
      let lastError: unknown;

      for (let i = 0; i < attempts; i++) {
        try {
          return await this.computation();
        } catch (error) {
          lastError = error;
          if (i < attempts - 1) {
            await new Promise((resolve) => setTimeout(resolve, delay * Math.pow(2, i)));
          }
        }
      }

      throw lastError;
    });
  }
}

export const Task = {
  of: <T>(value: T): Task<T> => {
    return new TaskImpl(() => Promise.resolve(value));
  },

  fromPromise: <T>(promise: Promise<T>): Task<T> => {
    return new TaskImpl(() => promise);
  },

  fromFunction: <T>(fn: () => Promise<T>): Task<T> => {
    return new TaskImpl(fn);
  },

  delay: (ms: number): Task<void> => {
    return new TaskImpl(() => new Promise((resolve) => setTimeout(resolve, ms)));
  },

  parallel: <T>(tasks: Task<T>[]): Task<T[]> => {
    return new TaskImpl(() => Promise.all(tasks.map((task) => task.run())));
  },

  sequence: <T>(tasks: Task<T>[]): Task<T[]> => {
    return new TaskImpl(async () => {
      const results: T[] = [];
      for (const task of tasks) {
        results.push(await task.run());
      }
      return results;
    });
  },
};
```

## IO Monad

```typescript
// src/core/utils/fp-patterns.ts
export interface IO<T> {
  map<U>(fn: (value: T) => U): IO<U>;
  flatMap<U>(fn: (value: T) => IO<U>): IO<U>;
  run(): T;
}

class IOImpl<T> implements IO<T> {
  constructor(private readonly computation: () => T) {}

  map<U>(fn: (value: T) => U): IO<U> {
    return new IOImpl(() => fn(this.computation()));
  }

  flatMap<U>(fn: (value: T) => IO<U>): IO<U> {
    return new IOImpl(() => fn(this.computation()).run());
  }

  run(): T {
    return this.computation();
  }
}

export const IO = {
  of: <T>(value: T): IO<T> => {
    return new IOImpl(() => value);
  },

  fromFunction: <T>(fn: () => T): IO<T> => {
    return new IOImpl(fn);
  },

  // Side effect operations
  log: (message: string): IO<void> => {
    return new IOImpl(() => console.log(message));
  },

  random: (): IO<number> => {
    return new IOImpl(() => Math.random());
  },

  now: (): IO<Date> => {
    return new IOImpl(() => new Date());
  },
};
```

## Functional Utilities

```typescript
// src/core/utils/functional.utils.ts

// Composition functions
export const pipe =
  <T>(...fns: Array<(arg: T) => T>) =>
  (value: T): T =>
    fns.reduce((acc, fn) => fn(acc), value);

export const compose =
  <T>(...fns: Array<(arg: T) => T>) =>
  (value: T): T =>
    fns.reduceRight((acc, fn) => fn(acc), value);

// Advanced pipe for different types
export const pipeAsync =
  <T>(...fns: Array<(arg: T) => Promise<T>>) =>
  async (value: T): Promise<T> => {
    let result = value;
    for (const fn of fns) {
      result = await fn(result);
    }
    return result;
  };

// Currying
export const curry =
  <T extends any[], R>(fn: (...args: T) => R) =>
  (...args: Partial<T>): any =>
    args.length >= fn.length
      ? fn(...(args as T))
      : (...nextArgs: any[]) => curry(fn)(...args, ...nextArgs);

// Partial application
export const partial =
  <T extends any[], R>(fn: (...args: T) => R, ...partialArgs: Partial<T>) =>
  (...remainingArgs: any[]): R => {
    return fn(...(partialArgs.concat(remainingArgs) as T));
  };

// Memoization
export const memoize = <T extends any[], R>(
  fn: (...args: T) => R,
  keyFn?: (...args: T) => string,
): ((...args: T) => R) => {
  const cache = new Map<string, R>();

  return (...args: T): R => {
    const key = keyFn ? keyFn(...args) : JSON.stringify(args);

    if (cache.has(key)) {
      return cache.get(key)!;
    }

    const result = fn(...args);
    cache.set(key, result);
    return result;
  };
};

// Debounce and Throttle
export const debounce = <T extends any[]>(fn: (...args: T) => void, delay: number) => {
  let timeoutId: NodeJS.Timeout;

  return (...args: T): void => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
};

export const throttle = <T extends any[]>(fn: (...args: T) => void, limit: number) => {
  let inThrottle: boolean;

  return (...args: T): void => {
    if (!inThrottle) {
      fn(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// Function combinators
export const always =
  <T>(value: T) =>
  (): T =>
    value;

export const identity = <T>(value: T): T => value;

export const flip =
  <A, B, C>(fn: (a: A, b: B) => C) =>
  (b: B, a: A): C =>
    fn(a, b);

export const not =
  <T extends any[]>(fn: (...args: T) => boolean) =>
  (...args: T): boolean =>
    !fn(...args);

export const once = <T extends any[], R>(fn: (...args: T) => R) => {
  let called = false;
  let result: R;

  return (...args: T): R => {
    if (!called) {
      called = true;
      result = fn(...args);
    }
    return result;
  };
};

// Array utilities
export const map =
  <T, U>(fn: (item: T) => U) =>
  (array: T[]): U[] =>
    array.map(fn);

export const filter =
  <T>(predicate: (item: T) => boolean) =>
  (array: T[]): T[] =>
    array.filter(predicate);

export const reduce =
  <T, U>(fn: (acc: U, item: T) => U, initial: U) =>
  (array: T[]): U =>
    array.reduce(fn, initial);

export const find =
  <T>(predicate: (item: T) => boolean) =>
  (array: T[]): Maybe<T> =>
    Maybe.fromNullable(array.find(predicate));

export const head = <T>(array: T[]): Maybe<T> => Maybe.fromNullable(array[0]);

export const tail = <T>(array: T[]): T[] => array.slice(1);

export const last = <T>(array: T[]): Maybe<T> => Maybe.fromNullable(array[array.length - 1]);

// Object utilities
export const prop =
  <T, K extends keyof T>(key: K) =>
  (obj: T): T[K] =>
    obj[key];

export const pick =
  <T, K extends keyof T>(keys: K[]) =>
  (obj: T): Pick<T, K> => {
    const result = {} as Pick<T, K>;
    keys.forEach((key) => {
      result[key] = obj[key];
    });
    return result;
  };

export const omit =
  <T, K extends keyof T>(keys: K[]) =>
  (obj: T): Omit<T, K> => {
    const result = { ...obj };
    keys.forEach((key) => {
      delete result[key];
    });
    return result;
  };

// Validation utilities
export const isNil = (value: any): value is null | undefined => value == null;

export const isEmpty = (value: any): boolean => {
  if (isNil(value)) return true;
  if (typeof value === 'string') return value.length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

export const isNotEmpty = (value: any): boolean => !isEmpty(value);

// Async utilities
export const asyncMap =
  <T, U>(fn: (item: T) => Promise<U>) =>
  async (array: T[]): Promise<U[]> => {
    return Promise.all(array.map(fn));
  };

export const asyncFilter =
  <T>(predicate: (item: T) => Promise<boolean>) =>
  async (array: T[]): Promise<T[]> => {
    const results = await Promise.all(array.map(predicate));
    return array.filter((_, index) => results[index]);
  };

export const asyncReduce =
  <T, U>(fn: (acc: U, item: T) => Promise<U>, initial: U) =>
  async (array: T[]): Promise<U> => {
    let result = initial;
    for (const item of array) {
      result = await fn(result, item);
    }
    return result;
  };

// Error handling utilities
export const tryCatch = <T, E = Error>(
  fn: () => T,
  onError: (error: unknown) => E,
): Either<E, T> => {
  try {
    return Either.right(fn());
  } catch (error) {
    return Either.left(onError(error));
  }
};

export const tryCatchAsync = <T, E = Error>(
  fn: () => Promise<T>,
  onError: (error: unknown) => E,
): Task<Either<E, T>> => {
  return Task.fromFunction(async () => {
    try {
      const result = await fn();
      return Either.right<E, T>(result);
    } catch (error) {
      return Either.left<E, T>(onError(error));
    }
  });
};
```

## Functional Composables

```typescript
// src/core/composables/useFunctional.ts
export const useFunctional = () => {
  // Maybe utilities
  const safeProp =
    <T, K extends keyof T>(key: K) =>
    (obj: T | null | undefined): Maybe<T[K]> => {
      return Maybe.fromNullable(obj).map((o) => o[key]);
    };

  const safeCall =
    <T extends any[], R>(fn: (...args: T) => R) =>
    (...args: T): Maybe<R> => {
      return tryCatch(
        () => fn(...args),
        () => null,
      ).fold(
        () => Maybe.none<R>(),
        (value) => Maybe.some(value),
      );
    };

  // Either utilities
  const validateAndTransform = <T, U, E>(
    value: T,
    validator: (value: T) => Either<E, T>,
    transformer: (value: T) => U,
  ): Either<E, U> => {
    return validator(value).map(transformer);
  };

  // Task utilities
  const fetchWithRetry = <T>(
    url: string,
    options?: RequestInit,
    retries: number = 3,
  ): Task<Either<Error, T>> => {
    return Task.fromFunction(async () => {
      try {
        const response = await fetch(url, options);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.json();
        return Either.right<Error, T>(data);
      } catch (error) {
        return Either.left<Error, T>(error as Error);
      }
    }).retry(retries);
  };

  // Reactive functional utilities
  const mapRef = <T, U>(sourceRef: Ref<T>, mapper: (value: T) => U): ComputedRef<U> => {
    return computed(() => mapper(sourceRef.value));
  };

  const filterRef = <T>(sourceRef: Ref<T[]>, predicate: (item: T) => boolean): ComputedRef<T[]> => {
    return computed(() => sourceRef.value.filter(predicate));
  };

  const reduceRef = <T, U>(
    sourceRef: Ref<T[]>,
    reducer: (acc: U, item: T) => U,
    initial: U,
  ): ComputedRef<U> => {
    return computed(() => sourceRef.value.reduce(reducer, initial));
  };

  return {
    // Monads
    Maybe,
    Either,
    Task,
    IO,

    // Utilities
    safeProp,
    safeCall,
    validateAndTransform,
    fetchWithRetry,

    // Reactive utilities
    mapRef,
    filterRef,
    reduceRef,

    // Core functional utilities
    pipe,
    compose,
    curry,
    memoize,
    debounce,
    throttle,

    // Array utilities
    map,
    filter,
    reduce,
    find,
    head,
    tail,
    last,

    // Object utilities
    prop,
    pick,
    omit,

    // Validation utilities
    isEmpty,
    isNotEmpty,
    isNil,

    // Error handling
    tryCatch,
    tryCatchAsync,
  };
};
```

## Functional Programming Examples

```typescript
// src/examples/functional-examples.ts

// Example 1: Safe data access with Maybe
const getUserEmail = (users: User[], userId: string): Maybe<string> => {
  return Maybe.fromNullable(users.find((u) => u.id === userId))
    .map((user) => user.email)
    .map((email) => email.toLowerCase());
};

// Example 2: Error handling with Either
const parseAndValidateAge = (input: string): Either<string, number> => {
  return Either.fromNullable(input, 'Input is required')
    .flatMap((str) => {
      const num = parseInt(str, 10);
      return isNaN(num)
        ? Either.left<string, number>('Invalid number')
        : Either.right<string, number>(num);
    })
    .flatMap((age) =>
      age >= 0 && age <= 150
        ? Either.right<string, number>(age)
        : Either.left<string, number>('Age must be between 0 and 150'),
    );
};

// Example 3: Async operations with Task
const fetchUserProfile = (userId: string): Task<Either<Error, UserProfile>> => {
  return Task.fromFunction(async () => {
    try {
      const response = await fetch(`/api/users/${userId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch user: ${response.statusText}`);
      }
      const data = await response.json();
      return Either.right<Error, UserProfile>(data);
    } catch (error) {
      return Either.left<Error, UserProfile>(error as Error);
    }
  })
    .timeout(5000)
    .retry(3);
};

// Example 4: Functional composition
const processUserData = pipe(
  (users: User[]) => users.filter((u) => u.isActive),
  (users: User[]) => users.map((u) => ({ id: u.id, email: u.email })),
  (users: { id: string; email: string }[]) => users.sort((a, b) => a.email.localeCompare(b.email)),
);

// Example 5: Curried validation functions
const validateLength = curry((min: number, max: number, value: string): Either<string, string> => {
  if (value.length < min) {
    return Either.left(`Minimum length is ${min}`);
  }
  if (value.length > max) {
    return Either.left(`Maximum length is ${max}`);
  }
  return Either.right(value);
});

const validateEmail = (email: string): Either<string, string> => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) ? Either.right(email) : Either.left('Invalid email format');
};

const validatePassword = validateLength(8, 50);

// Example 6: Combining validations
const validateUserInput = (input: {
  email: string;
  password: string;
}): Either<string[], { email: string; password: string }> => {
  const emailResult = validateEmail(input.email);
  const passwordResult = validatePassword(input.password);

  const errors: string[] = [];

  if (emailResult.isLeft()) {
    errors.push(
      emailResult.fold(
        (err) => err,
        () => '',
      ),
    );
  }

  if (passwordResult.isLeft()) {
    errors.push(
      passwordResult.fold(
        (err) => err,
        () => '',
      ),
    );
  }

  return errors.length > 0 ? Either.left(errors) : Either.right(input);
};
```
