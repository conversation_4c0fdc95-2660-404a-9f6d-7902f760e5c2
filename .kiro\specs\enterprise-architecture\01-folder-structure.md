# Proje Klasör Yapısı

## Genel Klasör Organizasyonu

```
src/
├── config/                    # Configuration layer
│   ├── index.ts              # Ana config export
│   ├── app.config.ts         # Uygulama ayarları
│   ├── api.config.ts         # API ayarları
│   ├── firebase.config.ts    # Firebase ayarları
│   ├── logger.config.ts      # Logger ayarları
│   ├── notification.config.ts # Notification ayarları
│   ├── database.config.ts    # Database ayarları
│   └── cache.config.ts       # Cache ayarları

├── core/                     # Core katmanı
│   ├── types/               # Genel tip tanımları
│   │   ├── index.ts
│   │   ├── api.types.ts
│   │   ├── error.types.ts
│   │   ├── config.types.ts
│   │   ├── entity.types.ts  # Domain entities
│   │   └── event.types.ts   # Event system types
│   │
│   ├── services/            # Core servisler
│   │   ├── api/
│   │   │   ├── index.ts
│   │   │   ├── api.factory.ts
│   │   │   ├── rest-api.service.ts
│   │   │   ├── firebase.service.ts
│   │   │   ├── graphql.service.ts    # GraphQL support
│   │   │   └── websocket.service.ts  # Real-time communication
│   │   │
│   │   ├── logger/
│   │   │   ├── index.ts
│   │   │   ├── logger.factory.ts
│   │   │   ├── console.logger.ts
│   │   │   ├── remote.logger.ts
│   │   │   └── file.logger.ts       # File logging
│   │   │
│   │   ├── notification/
│   │   │   ├── index.ts
│   │   │   ├── notification.factory.ts
│   │   │   ├── quasar.notification.ts
│   │   │   ├── push.notification.ts
│   │   │   └── email.notification.ts # Email notifications
│   │   │
│   │   ├── error/
│   │   │   ├── index.ts
│   │   │   ├── error-handler.service.ts
│   │   │   ├── error-reporter.service.ts
│   │   │   └── error-recovery.service.ts
│   │   │
│   │   ├── cache/           # Cache layer
│   │   │   ├── index.ts
│   │   │   ├── cache.factory.ts
│   │   │   ├── memory.cache.ts
│   │   │   ├── local-storage.cache.ts
│   │   │   └── redis.cache.ts
│   │   │
│   │   ├── security/        # Security services
│   │   │   ├── index.ts
│   │   │   ├── auth.service.ts
│   │   │   ├── permission.service.ts
│   │   │   └── encryption.service.ts
│   │   │
│   │   ├── validation/      # Validation services
│   │   │   ├── index.ts
│   │   │   ├── schema.validator.ts
│   │   │   └── business.validator.ts
│   │   │
│   │   └── events/          # Event system
│   │       ├── index.ts
│   │       ├── event-bus.service.ts
│   │       └── event-dispatcher.service.ts
│   │
│   ├── composables/         # Core composables
│   │   ├── index.ts
│   │   ├── useApi.ts
│   │   ├── useLogger.ts
│   │   ├── useNotification.ts
│   │   ├── useErrorHandler.ts
│   │   ├── useCache.ts      # Cache composable
│   │   ├── useAuth.ts       # Authentication composable
│   │   ├── usePermission.ts # Permission composable
│   │   ├── useValidation.ts # Validation composable
│   │   └── useEvents.ts     # Event system composable
│   │
│   ├── utils/               # Utility fonksiyonlar
│   │   ├── index.ts
│   │   ├── functional.utils.ts
│   │   ├── validation.utils.ts
│   │   ├── format.utils.ts
│   │   ├── crypto.utils.ts     # Encryption/hashing utilities
│   │   ├── date.utils.ts       # Date manipulation utilities
│   │   ├── string.utils.ts     # String manipulation utilities
│   │   ├── array.utils.ts      # Array utilities
│   │   ├── object.utils.ts     # Object utilities
│   │   └── performance.utils.ts # Performance monitoring
│   │
│   ├── providers/           # Dependency injection
│   │   ├── index.ts
│   │   ├── core.provider.ts
│   │   ├── service.provider.ts
│   │   └── module.provider.ts
│   │
│   └── decorators/          # Decorators
│       ├── index.ts
│       ├── cache.decorator.ts
│       ├── logger.decorator.ts
│       ├── validate.decorator.ts
│       └── permission.decorator.ts

├── domain/                  # Domain logic (DDD)
│   ├── entities/           # Domain entities
│   ├── repositories/       # Repository interfaces
│   ├── services/           # Domain services
│   ├── value-objects/      # Value objects
│   └── events/             # Domain events

├── infrastructure/          # Infrastructure layer
│   ├── repositories/       # Repository implementations
│   ├── external-services/  # External service integrations
│   └── adapters/           # External adapters

├── modules/                # Feature modules
│   ├── auth/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── composables/
│   │   ├── services/
│   │   ├── stores/
│   │   ├── routes/
│   │   ├── i18n/
│   │   ├── assets/
│   │   ├── layouts/
│   │   ├── types/
│   │   ├── validators/
│   │   └── tests/          # Module-specific tests
│   │
│   ├── dashboard/
│   │   └── ... (aynı yapı)
│   │
│   └── users/
│       └── ... (aynı yapı)

├── shared/                 # Paylaşılan bileşenler
│   ├── components/
│   │   ├── ui/            # UI components
│   │   ├── forms/         # Form components
│   │   ├── layout/        # Layout components
│   │   └── business/      # Business components
│   ├── composables/
│   ├── utils/
│   ├── constants/
│   ├── types/
│   └── styles/

├── assets/                 # Static assets
│   ├── images/
│   ├── icons/
│   ├── fonts/
│   └── styles/

├── boot/                   # Quasar boot files
│   ├── app.boot.ts
│   ├── api.boot.ts
│   ├── logger.boot.ts
│   ├── auth.boot.ts
│   └── i18n.boot.ts

├── layouts/                # Global layouts
├── pages/                  # Ana sayfalar

├── router/                 # Routing
│   ├── index.ts
│   ├── guards/
│   └── middleware/

├── stores/                 # Global stores (Pinia)
│   ├── index.ts
│   ├── app.store.ts
│   ├── auth.store.ts
│   └── user.store.ts

├── tests/                  # Test files
│   ├── unit/
│   ├── integration/
│   └── e2e/

└── types/                  # Global type definitions
    ├── index.ts
    ├── global.types.ts
    └── vendor.types.ts
```

## Modül Yapısı Şablonu

Her feature modülü aşağıdaki yapıyı takip eder:

```
src/modules/[module-name]/
├── components/             # Modül bileşenleri
│   ├── [ComponentName].vue
│   └── index.ts
├── pages/                  # Modül sayfaları
│   ├── [PageName].vue
│   └── index.ts
├── composables/            # Modül composable'ları
│   ├── use[Feature].ts
│   └── index.ts
├── services/               # Modül servisleri
│   ├── [service].service.ts
│   └── index.ts
├── stores/                 # Modül store'ları
│   ├── [store].store.ts
│   └── index.ts
├── routes/                 # Modül route'ları
│   └── [module].routes.ts
├── i18n/                   # Modül çevirileri
│   ├── en.json
│   ├── tr.json
│   └── index.ts
├── assets/                 # Modül varlıkları
├── layouts/                # Modül layout'ları
├── types/                  # Modül tip tanımları
│   ├── [type].types.ts
│   └── index.ts
├── validators/             # Modül doğrulayıcıları
│   ├── [validator].validator.ts
│   └── index.ts
└── tests/                  # Modül testleri
    ├── unit/
    ├── integration/
    └── e2e/
```

## Test Yapısı

```
src/tests/
├── unit/                   # Birim testleri
│   ├── core/
│   │   ├── services/
│   │   ├── utils/
│   │   └── composables/
│   ├── modules/
│   │   ├── auth/
│   │   └── dashboard/
│   └── shared/
├── integration/            # Entegrasyon testleri
│   ├── api/
│   ├── database/
│   └── services/
├── e2e/                    # End-to-end testleri
│   ├── auth/
│   ├── dashboard/
│   └── user-flows/
├── fixtures/               # Test verileri
├── mocks/                  # Mock'lar
└── utils/                  # Test yardımcıları
```

## Organizasyon İlkeleri

### 1. Katmanlı Mimari

- **Config**: Konfigürasyon yönetimi
- **Core**: Temel servisler ve yardımcılar
- **Domain**: İş mantığı (DDD)
- **Infrastructure**: Dış sistem entegrasyonları
- **Modules**: Özellik modülleri
- **Shared**: Paylaşılan bileşenler

### 2. Modülerlik

- Her özellik kendi modülünde
- Net sınırlar ve arayüzler
- Bağımsız geliştirme ve test

### 3. Yeniden Kullanılabilirlik

- Shared klasöründe ortak bileşenler
- Core katmanında temel servisler
- Utility fonksiyonları

### 4. Test Edilebilirlik

- Her katman için ayrı test klasörleri
- Mock'lar ve fixture'lar
- Birim, entegrasyon ve e2e testleri
