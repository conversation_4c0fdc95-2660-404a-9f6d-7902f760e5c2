# Konfigürasyon Tipleri ve Yönetimi

## Temel Konfigürasyon Tipleri

### Veri <PERSON>ğı Tipleri

```typescript
export type DataSource = 'restapi' | 'firebase' | 'graphql' | 'hybrid';
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export type NotificationProvider = 'quasar' | 'push' | 'email' | 'multiple';
export type CacheProvider = 'memory' | 'localStorage' | 'redis' | 'hybrid';
export type Environment = 'development' | 'staging' | 'production';
```

## Konfigürasyon Arayüzleri

### Uygulama Konfigürasyonu

```typescript
export interface AppConfig {
  name: string;
  version: string;
  environment: Environment;
  debug: boolean;
  baseUrl: string;
  apiVersion: string;
  features: {
    realTime: boolean;
    offline: boolean;
    analytics: boolean;
    monitoring: boolean;
  };
}
```

### API Konfigürasyonu

```typescript
export interface ApiConfig {
  dataSource: DataSource;
  baseURL: string;
  timeout: number;
  retryCount: number;
  retryDelay: number;
  circuitBreaker: {
    enabled: boolean;
    failureThreshold: number;
    resetTimeout: number;
  };
  rateLimiting: {
    enabled: boolean;
    maxRequests: number;
    windowMs: number;
  };
  compression: boolean;
  websocket?: {
    enabled: boolean;
    url: string;
    reconnectInterval: number;
    maxReconnectAttempts: number;
  };
}
```

### Güvenlik Konfigürasyonu

```typescript
export interface SecurityConfig {
  auth: {
    tokenExpiry: number;
    refreshTokenExpiry: number;
    sessionTimeout: number;
    maxLoginAttempts: number;
  };
  encryption: {
    algorithm: string;
    keySize: number;
    saltRounds: number;
  };
  csrf: {
    enabled: boolean;
    cookieName: string;
  };
  cors: {
    enabled: boolean;
    origins: string[];
    methods: string[];
  };
}
```

### Ana Konfigürasyon

```typescript
export interface CoreConfig {
  app: AppConfig;
  api: ApiConfig;
  firebase: FirebaseConfig;
  logger: LoggerConfig;
  notification: NotificationConfig;
  security: SecurityConfig;
  cache: CacheConfig;
  monitoring: MonitoringConfig;
}
```

## Ortam Değişkenleri

### .env Dosyası Şablonu

```bash
# Application Configuration
VITE_APP_NAME=MyApp
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development
VITE_APP_DEBUG=true
VITE_APP_BASE_URL=http://localhost:9000

# Data Source Configuration
VITE_DATA_SOURCE=restapi # restapi | firebase | graphql | hybrid

# REST API Configuration
VITE_API_BASE_URL=https://api.example.com
VITE_API_VERSION=v1
VITE_API_TIMEOUT=30000
VITE_API_RETRY_COUNT=3
VITE_API_RETRY_DELAY=1000

# Firebase Configuration
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef

# GraphQL Configuration
VITE_GRAPHQL_ENDPOINT=https://api.example.com/graphql
VITE_GRAPHQL_WS_ENDPOINT=wss://api.example.com/graphql

# Logger Configuration
VITE_LOG_LEVEL=info # debug | info | warn | error
VITE_LOG_PROVIDER=console # console | remote | file | multiple
VITE_LOG_REMOTE_URL=https://logs.example.com/api/logs

# Notification Configuration
VITE_NOTIFICATION_PROVIDER=quasar # quasar | push | email | multiple
VITE_NOTIFICATION_PUSH_VAPID_KEY=your-vapid-key
VITE_NOTIFICATION_EMAIL_SERVICE_URL=https://email.example.com/api/send

# Cache Configuration
VITE_CACHE_PROVIDER=memory # memory | localStorage | redis | hybrid
VITE_CACHE_TTL=300000
VITE_CACHE_MAX_SIZE=1000
VITE_REDIS_HOST=localhost
VITE_REDIS_PORT=6379
VITE_REDIS_PASSWORD=
VITE_REDIS_DB=0

# Security Configuration
VITE_AUTH_TOKEN_EXPIRY=3600000
VITE_AUTH_REFRESH_TOKEN_EXPIRY=604800000
VITE_AUTH_SESSION_TIMEOUT=1800000
VITE_AUTH_MAX_LOGIN_ATTEMPTS=5

# Monitoring Configuration
VITE_MONITORING_ENABLED=true
VITE_MONITORING_PERFORMANCE_ENABLED=true
VITE_MONITORING_PERFORMANCE_SAMPLE_RATE=0.1
VITE_MONITORING_ERRORS_ENABLED=true
VITE_MONITORING_ERRORS_REMOTE_URL=https://errors.example.com/api/report
VITE_MONITORING_ANALYTICS_ENABLED=true
VITE_MONITORING_ANALYTICS_PROVIDER=google
VITE_MONITORING_ANALYTICS_TRACKING_ID=GA_TRACKING_ID
```

## Konfigürasyon Yönetimi

### Konfigürasyon Factory

```typescript
// src/config/index.ts
export const createCoreConfig = (): CoreConfig => {
  return {
    app: createAppConfig(),
    api: createApiConfig(),
    firebase: createFirebaseConfig(),
    logger: createLoggerConfig(),
    notification: createNotificationConfig(),
    security: createSecurityConfig(),
    cache: createCacheConfig(),
    monitoring: createMonitoringConfig(),
  };
};
```

### Ortam Bazlı Konfigürasyon

```typescript
// src/config/app.config.ts
export const createAppConfig = (): AppConfig => {
  const environment = import.meta.env.VITE_APP_ENVIRONMENT as Environment;

  return {
    name: import.meta.env.VITE_APP_NAME || 'MyApp',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    environment,
    debug: import.meta.env.VITE_APP_DEBUG === 'true',
    baseUrl: import.meta.env.VITE_APP_BASE_URL || 'http://localhost:9000',
    apiVersion: import.meta.env.VITE_API_VERSION || 'v1',
    features: {
      realTime: environment !== 'development',
      offline: true,
      analytics: environment === 'production',
      monitoring: environment !== 'development',
    },
  };
};
```

## Konfigürasyon Doğrulama

### Şema Doğrulama

```typescript
// src/config/validation.ts
export const validateConfig = (config: CoreConfig): ValidationResult => {
  const errors: string[] = [];

  // App config validation
  if (!config.app.name) errors.push('App name is required');
  if (!config.app.version) errors.push('App version is required');

  // API config validation
  if (!config.api.baseURL) errors.push('API base URL is required');
  if (config.api.timeout < 1000) errors.push('API timeout must be at least 1000ms');

  // Security config validation
  if (config.security.auth.tokenExpiry < 300000) {
    errors.push('Token expiry must be at least 5 minutes');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
```

## Konfigürasyon Kullanımı

### Dependency Injection ile Kullanım

```typescript
// src/boot/core.boot.ts
export default boot(({ app }) => {
  const coreConfig = createCoreConfig();

  // Validate configuration
  const validation = validateConfig(coreConfig);
  if (!validation.isValid) {
    throw new Error(`Configuration validation failed: ${validation.errors.join(', ')}`);
  }

  // Provide configuration
  app.provide('coreConfig', coreConfig);
});
```

### Composable ile Erişim

```typescript
// src/core/composables/useConfig.ts
export const useConfig = () => {
  const config = inject<CoreConfig>('coreConfig');

  if (!config) {
    throw new Error('Core configuration not found');
  }

  return config;
};
```
