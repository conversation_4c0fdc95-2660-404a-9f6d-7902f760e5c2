# Gelişmiş Hata İşleme Sistemi

## Hata Tipleri ve Sınıflandırma

### <PERSON><PERSON>

```typescript
// src/core/services/error/error-handler.service.ts
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  BUSINESS = 'BUSINESS',
  SYSTEM = 'SYSTEM',
  UNKNOWN = 'UNKNOWN',
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}
```

### Hata Modeli

```typescript
export interface AppError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  stack?: string;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  url?: string;
  component?: string;
  recoverable: boolean;
}
```

## Hata İşleyici Servisi

### Ana Hata <PERSON>ley<PERSON>

```typescript
export interface ErrorHandlerService {
  handle: (error: unknown, context?: any) => Promise<AppError>;
  report: (error: AppError) => Promise<void>;
  recover: (error: AppError) => Promise<boolean>;
  registerRecoveryStrategy: (strategy: ErrorRecoveryStrategy) => void;
}

const createErrorHandlerService = (
  logger: LoggerService,
  notification: NotificationService,
): ErrorHandlerService => {
  const recoveryStrategies: ErrorRecoveryStrategy[] = [];

  const createAppError = (error: unknown, context?: any): AppError => {
    const id = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = new Date();

    if (error instanceof Error) {
      return {
        id,
        type: determineErrorType(error),
        severity: determineSeverity(error),
        message: error.message,
        details: context,
        stack: error.stack,
        timestamp,
        userId: context?.userId,
        sessionId: context?.sessionId,
        userAgent: navigator.userAgent,
        url: window.location.href,
        component: context?.component,
        recoverable: isRecoverable(error),
      };
    }

    return {
      id,
      type: ErrorType.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: String(error),
      details: context,
      timestamp,
      userId: context?.userId,
      sessionId: context?.sessionId,
      userAgent: navigator.userAgent,
      url: window.location.href,
      component: context?.component,
      recoverable: false,
    };
  };

  const determineErrorType = (error: Error): ErrorType => {
    if (error.name === 'ValidationError') return ErrorType.VALIDATION;
    if (error.name === 'NetworkError') return ErrorType.NETWORK;
    if (error.name === 'AuthenticationError') return ErrorType.AUTHENTICATION;
    if (error.name === 'AuthorizationError') return ErrorType.AUTHORIZATION;
    if (error.name === 'BusinessError') return ErrorType.BUSINESS;
    return ErrorType.SYSTEM;
  };

  const determineSeverity = (error: Error): ErrorSeverity => {
    if (error.name === 'ValidationError') return ErrorSeverity.LOW;
    if (error.name === 'NetworkError') return ErrorSeverity.MEDIUM;
    if (error.name === 'AuthenticationError') return ErrorSeverity.HIGH;
    if (error.name === 'SystemError') return ErrorSeverity.CRITICAL;
    return ErrorSeverity.MEDIUM;
  };

  const isRecoverable = (error: Error): boolean => {
    return ['NetworkError', 'ValidationError'].includes(error.name);
  };

  return {
    async handle(error: unknown, context?: any): Promise<AppError> {
      const appError = createAppError(error, context);

      logger.error(
        `Error handled: ${appError.message}`,
        {
          error: appError,
          context,
        },
        'ErrorHandler',
      );

      // Önem derecesine göre kullanıcı bildirimi göster
      if (
        appError.severity === ErrorSeverity.HIGH ||
        appError.severity === ErrorSeverity.CRITICAL
      ) {
        notification.show({
          type: NotificationType.ERROR,
          message: appError.message,
          persistent: true,
        });
      }

      return appError;
    },

    async report(error: AppError): Promise<void> {
      // Dış servise raporla
      try {
        await fetch('/api/errors', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(error),
        });
      } catch (reportError) {
        logger.error('Failed to report error', reportError, 'ErrorHandler');
      }
    },

    async recover(error: AppError): Promise<boolean> {
      if (!error.recoverable) return false;

      for (const strategy of recoveryStrategies) {
        if (strategy.canRecover(error)) {
          try {
            const recovered = await strategy.recover(error);
            if (recovered) {
              logger.info(`Error recovered: ${error.id}`, { error }, 'ErrorHandler');
              return true;
            }
          } catch (recoveryError) {
            logger.error('Recovery failed', recoveryError, 'ErrorHandler');
          }
        }
      }

      return false;
    },

    registerRecoveryStrategy(strategy: ErrorRecoveryStrategy): void {
      recoveryStrategies.push(strategy);
    },
  };
};
```

## Kurtarma Stratejileri

### Kurtarma Arayüzü

```typescript
export interface ErrorRecoveryStrategy {
  canRecover: (error: AppError) => boolean;
  recover: (error: AppError) => Promise<boolean>;
}
```

### Ağ Hatası Kurtarma Stratejisi

```typescript
// src/core/services/error/strategies/network-recovery.strategy.ts
export class NetworkRecoveryStrategy implements ErrorRecoveryStrategy {
  private retryAttempts = new Map<string, number>();
  private maxRetries = 3;

  canRecover(error: AppError): boolean {
    return (
      error.type === ErrorType.NETWORK && (this.retryAttempts.get(error.id) || 0) < this.maxRetries
    );
  }

  async recover(error: AppError): Promise<boolean> {
    const attempts = this.retryAttempts.get(error.id) || 0;
    this.retryAttempts.set(error.id, attempts + 1);

    // Exponential backoff
    const delay = Math.pow(2, attempts) * 1000;
    await new Promise((resolve) => setTimeout(resolve, delay));

    try {
      // Orijinal isteği yeniden dene
      if (error.details?.originalRequest) {
        const response = await fetch(error.details.originalRequest.url, {
          ...error.details.originalRequest.options,
        });

        if (response.ok) {
          this.retryAttempts.delete(error.id);
          return true;
        }
      }
    } catch {
      // Retry başarısız
    }

    return false;
  }
}
```

### Kimlik Doğrulama Kurtarma Stratejisi

```typescript
// src/core/services/error/strategies/auth-recovery.strategy.ts
export class AuthRecoveryStrategy implements ErrorRecoveryStrategy {
  constructor(private authService: AuthService) {}

  canRecover(error: AppError): boolean {
    return error.type === ErrorType.AUTHENTICATION;
  }

  async recover(error: AppError): Promise<boolean> {
    try {
      // Refresh token ile yeniden kimlik doğrula
      const refreshed = await this.authService.refreshToken();

      if (refreshed) {
        // Orijinal isteği yeniden dene
        if (error.details?.originalRequest) {
          const response = await fetch(error.details.originalRequest.url, {
            ...error.details.originalRequest.options,
            headers: {
              ...error.details.originalRequest.options.headers,
              Authorization: `Bearer ${this.authService.getToken()}`,
            },
          });

          return response.ok;
        }
      }
    } catch {
      // Refresh başarısız, kullanıcıyı login sayfasına yönlendir
      this.authService.logout();
    }

    return false;
  }
}
```

## Özel Hata Sınıfları

### Temel Hata Sınıfı

```typescript
// src/core/types/error.types.ts
export abstract class BaseError extends Error {
  abstract readonly type: ErrorType;
  abstract readonly severity: ErrorSeverity;
  readonly timestamp: Date;
  readonly recoverable: boolean;

  constructor(
    message: string,
    public readonly details?: any,
    recoverable: boolean = false,
  ) {
    super(message);
    this.name = this.constructor.name;
    this.timestamp = new Date();
    this.recoverable = recoverable;

    // Stack trace'i düzelt
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}
```

### Spesifik Hata Sınıfları

```typescript
export class ValidationError extends BaseError {
  readonly type = ErrorType.VALIDATION;
  readonly severity = ErrorSeverity.LOW;

  constructor(
    message: string,
    public readonly field?: string,
    details?: any,
  ) {
    super(message, { field, ...details }, true);
  }
}

export class NetworkError extends BaseError {
  readonly type = ErrorType.NETWORK;
  readonly severity = ErrorSeverity.MEDIUM;

  constructor(
    message: string,
    public readonly status?: number,
    public readonly url?: string,
    details?: any,
  ) {
    super(message, { status, url, ...details }, true);
  }
}

export class AuthenticationError extends BaseError {
  readonly type = ErrorType.AUTHENTICATION;
  readonly severity = ErrorSeverity.HIGH;

  constructor(message: string = 'Authentication failed', details?: any) {
    super(message, details, true);
  }
}

export class AuthorizationError extends BaseError {
  readonly type = ErrorType.AUTHORIZATION;
  readonly severity = ErrorSeverity.HIGH;

  constructor(message: string = 'Access denied', details?: any) {
    super(message, details, false);
  }
}

export class BusinessError extends BaseError {
  readonly type = ErrorType.BUSINESS;
  readonly severity = ErrorSeverity.MEDIUM;

  constructor(
    message: string,
    public readonly code?: string,
    details?: any,
  ) {
    super(message, { code, ...details }, false);
  }
}
```

## Hata Yakalama Mekanizmaları

### Global Error Handler

```typescript
// src/boot/error.boot.ts
export default boot(({ app }) => {
  const errorHandler = inject<ErrorHandlerService>('errorHandler');

  // Vue error handler
  app.config.errorHandler = async (error, instance, info) => {
    const appError = await errorHandler.handle(error, {
      component: instance?.$options.name || 'Unknown',
      info,
    });

    await errorHandler.report(appError);
    await errorHandler.recover(appError);
  };

  // Unhandled promise rejection handler
  window.addEventListener('unhandledrejection', async (event) => {
    const appError = await errorHandler.handle(event.reason, {
      type: 'unhandledrejection',
    });

    await errorHandler.report(appError);
    await errorHandler.recover(appError);
  });

  // Global error handler
  window.addEventListener('error', async (event) => {
    const appError = await errorHandler.handle(event.error, {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    });

    await errorHandler.report(appError);
    await errorHandler.recover(appError);
  });
});
```

### Try-Catch Wrapper

```typescript
// src/core/utils/error.utils.ts
export const safeAsync = <T extends any[], R>(fn: (...args: T) => Promise<R>) => {
  return async (...args: T): Promise<R | null> => {
    try {
      return await fn(...args);
    } catch (error) {
      const errorHandler = inject<ErrorHandlerService>('errorHandler');
      if (errorHandler) {
        const appError = await errorHandler.handle(error, {
          function: fn.name,
          arguments: args,
        });
        await errorHandler.report(appError);
        await errorHandler.recover(appError);
      }
      return null;
    }
  };
};

// Kullanım örneği:
const fetchUserSafely = safeAsync(async (userId: string) => {
  const response = await fetch(`/api/users/${userId}`);
  if (!response.ok) throw new NetworkError('Failed to fetch user', response.status);
  return response.json();
});
```

## Error Boundary Composable

### Vue Error Boundary

```typescript
// src/core/composables/useErrorBoundary.ts
export const useErrorBoundary = () => {
  const errorHandler = inject<ErrorHandlerService>('errorHandler');
  const error = ref<AppError | null>(null);
  const hasError = computed(() => error.value !== null);

  const captureError = async (err: unknown, context?: any) => {
    if (errorHandler) {
      const appError = await errorHandler.handle(err, context);
      error.value = appError;

      await errorHandler.report(appError);
      const recovered = await errorHandler.recover(appError);

      if (recovered) {
        error.value = null;
      }

      return appError;
    }
    return null;
  };

  const clearError = () => {
    error.value = null;
  };

  const withErrorBoundary = <T extends any[], R>(fn: (...args: T) => Promise<R>) => {
    return async (...args: T): Promise<R | null> => {
      try {
        return await fn(...args);
      } catch (err) {
        await captureError(err, { function: fn.name, arguments: args });
        return null;
      }
    };
  };

  return {
    error: readonly(error),
    hasError,
    captureError,
    clearError,
    withErrorBoundary,
  };
};
```

## Hata Raporlama

### Remote Error Reporter

```typescript
// src/core/services/error/error-reporter.service.ts
export interface ErrorReporter {
  report(error: AppError): Promise<void>;
  reportBatch(errors: AppError[]): Promise<void>;
}

export class RemoteErrorReporter implements ErrorReporter {
  private queue: AppError[] = [];
  private isReporting = false;

  constructor(private config: { endpoint: string; batchSize: number; flushInterval: number }) {
    // Periyodik flush
    setInterval(() => this.flush(), config.flushInterval);
  }

  async report(error: AppError): Promise<void> {
    this.queue.push(error);

    if (this.queue.length >= this.config.batchSize) {
      await this.flush();
    }
  }

  async reportBatch(errors: AppError[]): Promise<void> {
    try {
      await fetch(this.config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ errors }),
      });
    } catch (reportError) {
      console.error('Failed to report errors:', reportError);
    }
  }

  private async flush(): Promise<void> {
    if (this.isReporting || this.queue.length === 0) return;

    this.isReporting = true;
    const errors = this.queue.splice(0);

    try {
      await this.reportBatch(errors);
    } catch {
      // Başarısız olan hataları geri koy
      this.queue.unshift(...errors);
    } finally {
      this.isReporting = false;
    }
  }
}
```

## Hata İzleme ve Analiz

### Error Analytics

```typescript
// src/core/services/error/error-analytics.service.ts
export interface ErrorAnalytics {
  track(error: AppError): void;
  getErrorStats(): ErrorStats;
  getErrorTrends(): ErrorTrend[];
}

export interface ErrorStats {
  totalErrors: number;
  errorsByType: Record<ErrorType, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  topErrors: Array<{ message: string; count: number }>;
}

export interface ErrorTrend {
  date: string;
  count: number;
  type: ErrorType;
}

export class ErrorAnalyticsService implements ErrorAnalytics {
  private errors: AppError[] = [];
  private maxErrors = 1000;

  track(error: AppError): void {
    this.errors.push(error);

    // Limit kontrolü
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors);
    }
  }

  getErrorStats(): ErrorStats {
    const totalErrors = this.errors.length;

    const errorsByType = this.errors.reduce(
      (acc, error) => {
        acc[error.type] = (acc[error.type] || 0) + 1;
        return acc;
      },
      {} as Record<ErrorType, number>,
    );

    const errorsBySeverity = this.errors.reduce(
      (acc, error) => {
        acc[error.severity] = (acc[error.severity] || 0) + 1;
        return acc;
      },
      {} as Record<ErrorSeverity, number>,
    );

    const messageCount = this.errors.reduce(
      (acc, error) => {
        acc[error.message] = (acc[error.message] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const topErrors = Object.entries(messageCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([message, count]) => ({ message, count }));

    return {
      totalErrors,
      errorsByType,
      errorsBySeverity,
      topErrors,
    };
  }

  getErrorTrends(): ErrorTrend[] {
    const trends = new Map<string, Map<ErrorType, number>>();

    this.errors.forEach((error) => {
      const date = error.timestamp.toISOString().split('T')[0];

      if (!trends.has(date)) {
        trends.set(date, new Map());
      }

      const dayTrends = trends.get(date)!;
      dayTrends.set(error.type, (dayTrends.get(error.type) || 0) + 1);
    });

    const result: ErrorTrend[] = [];

    trends.forEach((typeCounts, date) => {
      typeCounts.forEach((count, type) => {
        result.push({ date, count, type });
      });
    });

    return result.sort((a, b) => a.date.localeCompare(b.date));
  }
}
```

Bu hata işleme sistemi, uygulamadaki tüm hataları merkezi olarak yönetir, kategorize eder ve uygun kurtarma stratejilerini uygular. Sistem ayrıca hataları analiz ederek geliştiricilere değerli içgörüler sağlar.
