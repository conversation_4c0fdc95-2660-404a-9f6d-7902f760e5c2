# Mod<PERSON>l Yapısı Şablonu

## Temel Modül Yapısı

```
src/modules/<module-name>/
├── components/          # Vue bileşenleri
│   ├── forms/          # Form bileşenleri
│   ├── tables/         # Tablo bileşenleri
│   ├── cards/          # Kart bileşenleri
│   └── modals/         # Modal bileşenleri
├── pages/              # Sayfa bileşenleri
├── composables/        # Vue composable'ları
├── services/           # İş mantığı servisleri
├── stores/             # Pinia store'ları
├── routes/             # Modül rotaları
├── i18n/               # Çeviri dosyaları
├── assets/             # Statik dosyalar
├── layouts/            # Düzen bileşenleri (gerekirse)
├── types/              # TypeScript tip tanımları
├── validators/         # Validation kuralları
├── utils/              # Yardımcı fonksiyonlar
├── constants/          # Sabitler
└── tests/              # Test dosyaları
    ├── unit/
    ├── integration/
    └── e2e/
```

## Auth Modülü Örneği

### Types

```typescript
// src/modules/auth/types/auth.types.ts
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  acceptTerms: boolean;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  avatar?: string;
  roles: string[];
  permissions: string[];
  isActive: boolean;
  emailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### Services

```typescript
// src/modules/auth/services/auth.service.ts
export class AuthService {
  constructor(
    private readonly apiService: ApiService,
    private readonly logger: LoggerService,
  ) {}

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      this.logger.info('Login attempt', { email: credentials.email }, 'AuthService');

      const response = await this.apiService.post<AuthResponse>('/auth/login', {
        email: credentials.email,
        password: credentials.password,
        rememberMe: credentials.rememberMe,
      });

      this.logger.info('Login successful', { userId: response.user.id }, 'AuthService');
      return response;
    } catch (error) {
      this.logger.error('Login failed', error, 'AuthService');
      throw error;
    }
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      this.logger.info('Registration attempt', { email: data.email }, 'AuthService');

      const response = await this.apiService.post<AuthResponse>('/auth/register', {
        email: data.email,
        password: data.password,
        firstName: data.firstName,
        lastName: data.lastName,
      });

      this.logger.info('Registration successful', { userId: response.user.id }, 'AuthService');
      return response;
    } catch (error) {
      this.logger.error('Registration failed', error, 'AuthService');
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      await this.apiService.post('/auth/logout');
      this.logger.info('Logout successful', {}, 'AuthService');
    } catch (error) {
      this.logger.error('Logout failed', error, 'AuthService');
      throw error;
    }
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      const response = await this.apiService.post<AuthResponse>('/auth/refresh', {
        refreshToken,
      });

      this.logger.info('Token refresh successful', {}, 'AuthService');
      return response;
    } catch (error) {
      this.logger.error('Token refresh failed', error, 'AuthService');
      throw error;
    }
  }

  async requestPasswordReset(email: string): Promise<void> {
    try {
      await this.apiService.post('/auth/password-reset', { email });
      this.logger.info('Password reset requested', { email }, 'AuthService');
    } catch (error) {
      this.logger.error('Password reset request failed', error, 'AuthService');
      throw error;
    }
  }

  async confirmPasswordReset(data: PasswordResetConfirm): Promise<void> {
    try {
      await this.apiService.post('/auth/password-reset/confirm', {
        token: data.token,
        newPassword: data.newPassword,
      });

      this.logger.info('Password reset confirmed', {}, 'AuthService');
    } catch (error) {
      this.logger.error('Password reset confirmation failed', error, 'AuthService');
      throw error;
    }
  }

  async verifyEmail(token: string): Promise<void> {
    try {
      await this.apiService.post('/auth/verify-email', { token });
      this.logger.info('Email verification successful', {}, 'AuthService');
    } catch (error) {
      this.logger.error('Email verification failed', error, 'AuthService');
      throw error;
    }
  }

  async getCurrentUser(): Promise<User> {
    try {
      const response = await this.apiService.get<User>('/auth/me');
      return response;
    } catch (error) {
      this.logger.error('Get current user failed', error, 'AuthService');
      throw error;
    }
  }
}
```

### Store

```typescript
// src/modules/auth/stores/auth.store.ts
export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null);
  const token = ref<string | null>(null);
  const refreshToken = ref<string | null>(null);
  const isLoading = ref(false);
  const lastActivity = ref<Date | null>(null);

  // Services
  const authService = inject<AuthService>('authService')!;
  const { success, error: showError } = useNotification();
  const { emit } = useEvents();

  // Getters
  const isAuthenticated = computed(() => !!user.value && !!token.value);
  const userFullName = computed(() => user.value?.fullName || '');
  const userRoles = computed(() => user.value?.roles || []);
  const userPermissions = computed(() => user.value?.permissions || []);

  // Actions
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    isLoading.value = true;

    try {
      const response = await authService.login(credentials);

      user.value = response.user;
      token.value = response.token;
      refreshToken.value = response.refreshToken;
      lastActivity.value = new Date();

      // Persist to localStorage if remember me
      if (credentials.rememberMe) {
        localStorage.setItem('auth.token', response.token);
        localStorage.setItem('auth.refreshToken', response.refreshToken);
      }

      await emit('auth.login', { user: response.user });
      success('Başarıyla giriş yapıldı');

      return true;
    } catch (error) {
      showError('Giriş yapılamadı. Lütfen bilgilerinizi kontrol edin.');
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  const register = async (data: RegisterData): Promise<boolean> => {
    isLoading.value = true;

    try {
      const response = await authService.register(data);

      user.value = response.user;
      token.value = response.token;
      refreshToken.value = response.refreshToken;
      lastActivity.value = new Date();

      await emit('auth.register', { user: response.user });
      success('Hesabınız başarıyla oluşturuldu');

      return true;
    } catch (error) {
      showError('Kayıt olunamadı. Lütfen bilgilerinizi kontrol edin.');
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  const logout = async (): Promise<void> => {
    isLoading.value = true;

    try {
      await authService.logout();
    } catch (error) {
      // Continue with logout even if API call fails
    }

    user.value = null;
    token.value = null;
    refreshToken.value = null;
    lastActivity.value = null;

    // Clear localStorage
    localStorage.removeItem('auth.token');
    localStorage.removeItem('auth.refreshToken');

    await emit('auth.logout', {});
    success('Başarıyla çıkış yapıldı');

    isLoading.value = false;
  };

  const refreshAuthToken = async (): Promise<boolean> => {
    if (!refreshToken.value) return false;

    try {
      const response = await authService.refreshToken(refreshToken.value);

      user.value = response.user;
      token.value = response.token;
      refreshToken.value = response.refreshToken;
      lastActivity.value = new Date();

      // Update localStorage
      localStorage.setItem('auth.token', response.token);
      localStorage.setItem('auth.refreshToken', response.refreshToken);

      return true;
    } catch (error) {
      await logout();
      return false;
    }
  };

  const updateLastActivity = (): void => {
    lastActivity.value = new Date();
  };

  const initializeAuth = async (): Promise<void> => {
    const storedToken = localStorage.getItem('auth.token');
    const storedRefreshToken = localStorage.getItem('auth.refreshToken');

    if (storedToken && storedRefreshToken) {
      token.value = storedToken;
      refreshToken.value = storedRefreshToken;

      // Try to get current user
      try {
        user.value = await authService.getCurrentUser();
        lastActivity.value = new Date();
      } catch (error) {
        // Token might be expired, try to refresh
        const refreshed = await refreshAuthToken();
        if (!refreshed) {
          await logout();
        }
      }
    }
  };

  return {
    // State
    user: readonly(user),
    token: readonly(token),
    isLoading: readonly(isLoading),
    lastActivity: readonly(lastActivity),

    // Getters
    isAuthenticated,
    userFullName,
    userRoles,
    userPermissions,

    // Actions
    login,
    register,
    logout,
    refreshAuthToken,
    updateLastActivity,
    initializeAuth,
  };
});
```

### Composables

```typescript
// src/modules/auth/composables/useAuth.ts
export const useAuth = () => {
  const authStore = useAuthStore();
  const router = useRouter();

  const loginAndRedirect = async (
    credentials: LoginCredentials,
    redirectTo?: string,
  ): Promise<boolean> => {
    const success = await authStore.login(credentials);

    if (success) {
      const redirect =
        redirectTo || (router.currentRoute.value.query.redirect as string) || '/dashboard';
      await router.push(redirect);
    }

    return success;
  };

  const logoutAndRedirect = async (): Promise<void> => {
    await authStore.logout();
    await router.push('/auth/login');
  };

  const requireAuth = (): void => {
    if (!authStore.isAuthenticated) {
      router.push({
        path: '/auth/login',
        query: { redirect: router.currentRoute.value.fullPath },
      });
    }
  };

  const requireRole = (role: string): boolean => {
    return authStore.userRoles.includes(role);
  };

  const requirePermission = (permission: string): boolean => {
    return authStore.userPermissions.includes(permission);
  };

  return {
    // Store state
    user: authStore.user,
    isAuthenticated: authStore.isAuthenticated,
    isLoading: authStore.isLoading,
    userFullName: authStore.userFullName,
    userRoles: authStore.userRoles,
    userPermissions: authStore.userPermissions,

    // Actions
    login: authStore.login,
    register: authStore.register,
    logout: authStore.logout,
    refreshToken: authStore.refreshAuthToken,

    // Utilities
    loginAndRedirect,
    logoutAndRedirect,
    requireAuth,
    requireRole,
    requirePermission,
  };
};
```

### Validators

```typescript
// src/modules/auth/validators/auth.validators.ts
export const loginValidationSchema: ValidationSchema = {
  email: [
    {
      name: 'required',
      validate: (value: string) => !!value?.trim(),
      message: 'Email adresi gereklidir',
    },
    {
      name: 'email',
      validate: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
      message: 'Geçerli bir email adresi girin',
    },
  ],
  password: [
    {
      name: 'required',
      validate: (value: string) => !!value?.trim(),
      message: 'Şifre gereklidir',
    },
    {
      name: 'minLength',
      validate: (value: string) => value?.length >= 6,
      message: 'Şifre en az 6 karakter olmalıdır',
    },
  ],
};

export const registerValidationSchema: ValidationSchema = {
  email: [
    {
      name: 'required',
      validate: (value: string) => !!value?.trim(),
      message: 'Email adresi gereklidir',
    },
    {
      name: 'email',
      validate: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
      message: 'Geçerli bir email adresi girin',
    },
  ],
  password: [
    {
      name: 'required',
      validate: (value: string) => !!value?.trim(),
      message: 'Şifre gereklidir',
    },
    {
      name: 'minLength',
      validate: (value: string) => value?.length >= 8,
      message: 'Şifre en az 8 karakter olmalıdır',
    },
    {
      name: 'complexity',
      validate: (value: string) => {
        const hasUpper = /[A-Z]/.test(value);
        const hasLower = /[a-z]/.test(value);
        const hasNumber = /\d/.test(value);
        return hasUpper && hasLower && hasNumber;
      },
      message: 'Şifre büyük harf, küçük harf ve rakam içermelidir',
    },
  ],
  confirmPassword: [
    {
      name: 'required',
      validate: (value: string) => !!value?.trim(),
      message: 'Şifre tekrarı gereklidir',
    },
    {
      name: 'match',
      validate: (value: string, context: any) => value === context.password,
      message: 'Şifreler eşleşmiyor',
    },
  ],
  firstName: [
    {
      name: 'required',
      validate: (value: string) => !!value?.trim(),
      message: 'Ad gereklidir',
    },
    {
      name: 'minLength',
      validate: (value: string) => value?.trim().length >= 2,
      message: 'Ad en az 2 karakter olmalıdır',
    },
  ],
  lastName: [
    {
      name: 'required',
      validate: (value: string) => !!value?.trim(),
      message: 'Soyad gereklidir',
    },
    {
      name: 'minLength',
      validate: (value: string) => value?.trim().length >= 2,
      message: 'Soyad en az 2 karakter olmalıdır',
    },
  ],
  acceptTerms: [
    {
      name: 'required',
      validate: (value: boolean) => value === true,
      message: 'Kullanım şartlarını kabul etmelisiniz',
    },
  ],
};
```

### Components

```vue
<!-- src/modules/auth/components/forms/LoginForm.vue -->
<template>
  <q-form @submit="handleSubmit" class="login-form">
    <div class="text-h4 text-center q-mb-lg">Giriş Yap</div>

    <q-input
      v-model="form.values.email"
      label="Email Adresi"
      type="email"
      outlined
      :error="form.errors.email.length > 0"
      :error-message="form.errors.email[0]"
      class="q-mb-md"
    />

    <q-input
      v-model="form.values.password"
      label="Şifre"
      :type="showPassword ? 'text' : 'password'"
      outlined
      :error="form.errors.password.length > 0"
      :error-message="form.errors.password[0]"
      class="q-mb-md"
    >
      <template v-slot:append>
        <q-icon
          :name="showPassword ? 'visibility_off' : 'visibility'"
          class="cursor-pointer"
          @click="showPassword = !showPassword"
        />
      </template>
    </q-input>

    <q-checkbox v-model="form.values.rememberMe" label="Beni hatırla" class="q-mb-md" />

    <q-btn
      type="submit"
      label="Giriş Yap"
      color="primary"
      size="lg"
      :loading="isLoading"
      :disable="!form.isValid"
      class="full-width q-mb-md"
    />

    <div class="text-center">
      <router-link to="/auth/forgot-password" class="text-primary"> Şifremi unuttum </router-link>
    </div>

    <q-separator class="q-my-lg" />

    <div class="text-center">
      <span>Hesabınız yok mu? </span>
      <router-link to="/auth/register" class="text-primary"> Kayıt olun </router-link>
    </div>
  </q-form>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useAuth } from '../../composables/useAuth';
import { useValidation } from '@/core/composables/useValidation';
import { loginValidationSchema } from '../../validators/auth.validators';
import type { LoginCredentials } from '../../types/auth.types';

// Props & Emits
interface Props {
  redirectTo?: string;
}

const props = withDefaults(defineProps<Props>(), {
  redirectTo: '/dashboard',
});

const emit = defineEmits<{
  success: [user: User];
  error: [error: string];
}>();

// Composables
const { loginAndRedirect, isLoading } = useAuth();
const { useForm } = useValidation();

// State
const showPassword = ref(false);

// Form
const form = useForm<LoginCredentials>(
  {
    email: '',
    password: '',
    rememberMe: false,
  },
  loginValidationSchema,
);

// Methods
const handleSubmit = async () => {
  const isValid = await form.validate();
  if (!isValid) return;

  try {
    const success = await loginAndRedirect(form.values, props.redirectTo);
    if (success) {
      emit('success', form.values as any);
    } else {
      emit('error', 'Giriş yapılamadı');
    }
  } catch (error) {
    emit('error', 'Bir hata oluştu');
  }
};
</script>

<style scoped lang="scss">
.login-form {
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
}
</style>
```

### Routes

```typescript
// src/modules/auth/routes/auth.routes.ts
export const authRoutes: RouteRecordRaw[] = [
  {
    path: '/auth',
    component: () => import('../layouts/AuthLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'auth.login',
        component: () => import('../pages/LoginPage.vue'),
        meta: {
          requiresGuest: true,
          title: 'Giriş Yap',
        },
      },
      {
        path: 'register',
        name: 'auth.register',
        component: () => import('../pages/RegisterPage.vue'),
        meta: {
          requiresGuest: true,
          title: 'Kayıt Ol',
        },
      },
      {
        path: 'forgot-password',
        name: 'auth.forgot-password',
        component: () => import('../pages/ForgotPasswordPage.vue'),
        meta: {
          requiresGuest: true,
          title: 'Şifremi Unuttum',
        },
      },
      {
        path: 'reset-password/:token',
        name: 'auth.reset-password',
        component: () => import('../pages/ResetPasswordPage.vue'),
        meta: {
          requiresGuest: true,
          title: 'Şifre Sıfırla',
        },
      },
      {
        path: 'verify-email/:token',
        name: 'auth.verify-email',
        component: () => import('../pages/VerifyEmailPage.vue'),
        meta: {
          requiresGuest: true,
          title: 'Email Doğrula',
        },
      },
    ],
  },
];
```

### i18n

```json
// src/modules/auth/i18n/tr.json
{
  "auth": {
    "login": {
      "title": "Giriş Yap",
      "email": "Email Adresi",
      "password": "Şifre",
      "rememberMe": "Beni hatırla",
      "submit": "Giriş Yap",
      "forgotPassword": "Şifremi unuttum",
      "noAccount": "Hesabınız yok mu?",
      "register": "Kayıt olun",
      "success": "Başarıyla giriş yapıldı",
      "error": "Giriş yapılamadı. Lütfen bilgilerinizi kontrol edin."
    },
    "register": {
      "title": "Kayıt Ol",
      "firstName": "Ad",
      "lastName": "Soyad",
      "email": "Email Adresi",
      "password": "Şifre",
      "confirmPassword": "Şifre Tekrarı",
      "acceptTerms": "Kullanım şartlarını kabul ediyorum",
      "submit": "Kayıt Ol",
      "hasAccount": "Zaten hesabınız var mı?",
      "login": "Giriş yapın",
      "success": "Hesabınız başarıyla oluşturuldu",
      "error": "Kayıt olunamadı. Lütfen bilgilerinizi kontrol edin."
    },
    "validation": {
      "required": "Bu alan gereklidir",
      "email": "Geçerli bir email adresi girin",
      "minLength": "En az {min} karakter olmalıdır",
      "passwordMatch": "Şifreler eşleşmiyor",
      "acceptTerms": "Kullanım şartlarını kabul etmelisiniz"
    }
  }
}
```

### Tests

```typescript
// src/modules/auth/tests/unit/auth.service.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AuthService } from '../../services/auth.service';
import type { ApiService } from '@/core/services/api/api.service';
import type { LoggerService } from '@/core/services/logger/logger.service';

describe('AuthService', () => {
  let authService: AuthService;
  let mockApiService: ApiService;
  let mockLoggerService: LoggerService;

  beforeEach(() => {
    mockApiService = {
      post: vi.fn(),
      get: vi.fn(),
    } as any;

    mockLoggerService = {
      info: vi.fn(),
      error: vi.fn(),
    } as any;

    authService = new AuthService(mockApiService, mockLoggerService);
  });

  describe('login', () => {
    it('should login successfully', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockResponse = {
        user: { id: '1', email: '<EMAIL>' },
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
        expiresIn: 3600,
      };

      vi.mocked(mockApiService.post).mockResolvedValue(mockResponse);

      const result = await authService.login(credentials);

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/login', {
        email: credentials.email,
        password: credentials.password,
        rememberMe: undefined,
      });

      expect(result).toEqual(mockResponse);
      expect(mockLoggerService.info).toHaveBeenCalledWith(
        'Login successful',
        { userId: '1' },
        'AuthService',
      );
    });

    it('should handle login failure', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'wrong-password',
      };

      const mockError = new Error('Invalid credentials');
      vi.mocked(mockApiService.post).mockRejectedValue(mockError);

      await expect(authService.login(credentials)).rejects.toThrow('Invalid credentials');

      expect(mockLoggerService.error).toHaveBeenCalledWith(
        'Login failed',
        mockError,
        'AuthService',
      );
    });
  });
});
```

## Modül Entegrasyonu

### Module Provider

```typescript
// src/modules/auth/auth.module.ts
export const authModule = {
  install(app: App) {
    // Services
    const authService = new AuthService(
      app.config.globalProperties.$api,
      app.config.globalProperties.$logger,
    );

    app.provide('authService', authService);

    // Store
    const authStore = useAuthStore();

    // Initialize auth on app start
    authStore.initializeAuth();

    // Auto token refresh
    setInterval(
      () => {
        if (authStore.isAuthenticated) {
          authStore.refreshAuthToken();
        }
      },
      15 * 60 * 1000,
    ); // 15 minutes
  },
};
```

### Router Integration

```typescript
// src/router/index.ts
import { authRoutes } from '@/modules/auth/routes/auth.routes';

const routes: RouteRecordRaw[] = [
  ...authRoutes,
  // other routes
];

// Auth guard
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();

  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({
      path: '/auth/login',
      query: { redirect: to.fullPath },
    });
  } else if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next('/dashboard');
  } else {
    next();
  }
});
```
