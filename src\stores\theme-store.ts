import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { Dark, LocalStorage } from 'quasar';

// Tema seçeneklerini temsil eden bir type tanımlıyoruz.
// Bu, kodun daha güvenli ve okunabilir olmasını sağlar.
export type Theme = 'light' | 'dark';

const LOCAL_STORAGE_THEME_KEY = 'app-theme';

/**
 * Tema durumunu ve ilgili eyle<PERSON>i yöneten Pinia store'u.
 */
export const useThemeStore = defineStore('theme', () => {
    // --- STATE ---

    /**
     * Mevcut tema durumunu tutar. Varsayılan olarak 'light' başlar.
     * Boot dosyası bu değeri localStorage'dan okuyarak güncelleyecektir.
     */
    const theme = ref<Theme>('light');

    // --- GETTERS ---

    /**
     * Mevcut temanın koyu mod olup olmadığını kontrol eden bir computed property.
     * Bileşenlerde `theme.value === 'dark'` gibi ifadeler yerine bu getter kullanılır.
     */
    const isDark = computed(() => theme.value === 'dark');

    // --- ACTIONS ---

    /**
     * Temayı belirtilen değere ayarlar.
     * @param {Theme} newTheme - Ayarlanacak yeni tema ('light' veya 'dark').
     */
    function setTheme(newTheme: Theme): void {
        theme.value = newTheme;
    }

    /**
     * Mevcut temayı tersine çevirir (light -> dark, dark -> light).
     */
    function toggleTheme(): void {
        setTheme(theme.value === 'light' ? 'dark' : 'light');
    }

    /**
     * Tema durumunu başlangıçta LocalStorage'dan veya sistem tercihinden yükler.
     * Bu fonksiyon, boot dosyası tarafından çağrılır.
     */
    function initializeTheme() {
        const persistedTheme = LocalStorage.getItem<Theme>(LOCAL_STORAGE_THEME_KEY);

        if (persistedTheme) {
            setTheme(persistedTheme);
        } else {
            const prefersDark =
                window.matchMedia &&
                window.matchMedia('(prefers-color-scheme: dark)').matches;
            setTheme(prefersDark ? 'dark' : 'light');
        }
    }

    // --- SIDE EFFECTS ---

    /**
     * `theme` state'i her değiştiğinde bu watcher çalışır.
     * Sorumluluğu, uygulama genelindeki yan etkileri yönetmektir:
     * 1. Quasar'ın Dark mode plugin'ini günceller.
     * 2. Seçilen temayı tarayıcının localStorage'ına kaydeder.
     */
    watch(theme, (newTheme) => {
        // Quasar'ın global dark mode durumunu güncelle
        Dark.set(newTheme === 'dark');

        // Seçimi kalıcı hale getirmek için localStorage'a kaydet
        LocalStorage.set(LOCAL_STORAGE_THEME_KEY, newTheme);
    });

    return {
        theme,
        isDark,
        setTheme,
        toggleTheme,
        initializeTheme,
    };
});