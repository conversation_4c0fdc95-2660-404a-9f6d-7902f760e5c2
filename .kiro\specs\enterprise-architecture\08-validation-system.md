# Doğ<PERSON>lama Si<PERSON>mi (Validation System)

## Temel Doğrulama Arayüzleri

### Validation Rule Interface

```typescript
// src/core/services/validation/schema.validator.ts
export interface ValidationRule<T = any> {
  name: string;
  validate: (value: T, context?: any) => boolean | Promise<boolean>;
  message: string | ((value: T, context?: any) => string);
}

export interface ValidationSchema {
  [field: string]: ValidationRule[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  value: any;
}

export interface SchemaValidator {
  validate<T extends Record<string, any>>(
    data: T,
    schema: ValidationSchema,
    context?: any,
  ): Promise<ValidationResult>;
  addRule(rule: ValidationRule): void;
  getRule(name: string): ValidationRule | undefined;
}
```

## Schema Validator Implementasyonu

### Ana Validator Servisi

```typescript
const createSchemaValidator = (): SchemaValidator => {
  const rules = new Map<string, ValidationRule>();

  // Built-in kurallar
  const builtInRules: ValidationRule[] = [
    {
      name: 'required',
      validate: (value) => value !== null && value !== undefined && value !== '',
      message: 'Bu alan zorunludur',
    },
    {
      name: 'email',
      validate: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
      message: 'Geçerli bir e-posta adresi giriniz',
    },
    {
      name: 'minLength',
      validate: (value, context) => value.length >= context.minLength,
      message: (value, context) => `En az ${context.minLength} karakter olmalıdır`,
    },
    {
      name: 'maxLength',
      validate: (value, context) => value.length <= context.maxLength,
      message: (value, context) => `En fazla ${context.maxLength} karakter olmalıdır`,
    },
    {
      name: 'numeric',
      validate: (value) => !isNaN(Number(value)),
      message: 'Bu alan sayısal olmalıdır',
    },
    {
      name: 'integer',
      validate: (value) => Number.isInteger(Number(value)),
      message: 'Bu alan tam sayı olmalıdır',
    },
    {
      name: 'min',
      validate: (value, context) => Number(value) >= context.min,
      message: (value, context) => `En az ${context.min} olmalıdır`,
    },
    {
      name: 'max',
      validate: (value, context) => Number(value) <= context.max,
      message: (value, context) => `En fazla ${context.max} olmalıdır`,
    },
    {
      name: 'pattern',
      validate: (value, context) => new RegExp(context.pattern).test(value),
      message: (value, context) => context.message || 'Geçersiz format',
    },
    {
      name: 'url',
      validate: (value) => {
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      },
      message: 'Geçerli bir URL giriniz',
    },
    {
      name: 'phone',
      validate: (value) => /^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/\s/g, '')),
      message: 'Geçerli bir telefon numarası giriniz',
    },
    {
      name: 'date',
      validate: (value) => !isNaN(Date.parse(value)),
      message: 'Geçerli bir tarih giriniz',
    },
    {
      name: 'dateAfter',
      validate: (value, context) => new Date(value) > new Date(context.date),
      message: (value, context) => `${context.date} tarihinden sonra olmalıdır`,
    },
    {
      name: 'dateBefore',
      validate: (value, context) => new Date(value) < new Date(context.date),
      message: (value, context) => `${context.date} tarihinden önce olmalıdır`,
    },
  ];

  builtInRules.forEach((rule) => rules.set(rule.name, rule));

  return {
    async validate<T extends Record<string, any>>(
      data: T,
      schema: ValidationSchema,
      context?: any,
    ): Promise<ValidationResult> {
      const errors: ValidationError[] = [];

      for (const [field, fieldRules] of Object.entries(schema)) {
        const value = data[field];

        for (const rule of fieldRules) {
          try {
            const isValid = await rule.validate(value, context);
            if (!isValid) {
              const message =
                typeof rule.message === 'function' ? rule.message(value, context) : rule.message;

              errors.push({
                field,
                message,
                value,
              });
            }
          } catch (error) {
            errors.push({
              field,
              message: `Doğrulama hatası: ${error.message}`,
              value,
            });
          }
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },

    addRule(rule: ValidationRule): void {
      rules.set(rule.name, rule);
    },

    getRule(name: string): ValidationRule | undefined {
      return rules.get(name);
    },
  };
};
```

## Gelişmiş Doğrulama Kuralları

### Özel Doğrulama Kuralları

```typescript
// src/core/services/validation/custom-rules.ts
export const customValidationRules: ValidationRule[] = [
  {
    name: 'strongPassword',
    validate: (value: string) => {
      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumbers = /\d/.test(value);
      const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
      const isLongEnough = value.length >= 8;

      return hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar && isLongEnough;
    },
    message:
      'Şifre en az 8 karakter olmalı ve büyük harf, küçük harf, sayı ve özel karakter içermelidir',
  },
  {
    name: 'confirmPassword',
    validate: (value: string, context: any) => {
      return value === context.password;
    },
    message: 'Şifreler eşleşmiyor',
  },
  {
    name: 'uniqueEmail',
    validate: async (value: string, context: any) => {
      if (!context.apiService) return true;

      try {
        const response = await context.apiService.get(`/users/check-email?email=${value}`);
        return !response.exists;
      } catch {
        return true; // API hatası durumunda geç
      }
    },
    message: 'Bu e-posta adresi zaten kullanılıyor',
  },
  {
    name: 'fileSize',
    validate: (file: File, context: any) => {
      const maxSize = context.maxSize || 5 * 1024 * 1024; // 5MB default
      return file.size <= maxSize;
    },
    message: (file: File, context: any) => {
      const maxSizeMB = (context.maxSize || 5 * 1024 * 1024) / (1024 * 1024);
      return `Dosya boyutu en fazla ${maxSizeMB}MB olmalıdır`;
    },
  },
  {
    name: 'fileType',
    validate: (file: File, context: any) => {
      const allowedTypes = context.allowedTypes || [];
      return allowedTypes.includes(file.type);
    },
    message: (file: File, context: any) => {
      const allowedTypes = context.allowedTypes || [];
      return `İzin verilen dosya türleri: ${allowedTypes.join(', ')}`;
    },
  },
  {
    name: 'creditCard',
    validate: (value: string) => {
      // Luhn algoritması
      const digits = value.replace(/\D/g, '');
      let sum = 0;
      let isEven = false;

      for (let i = digits.length - 1; i >= 0; i--) {
        let digit = parseInt(digits[i]);

        if (isEven) {
          digit *= 2;
          if (digit > 9) {
            digit -= 9;
          }
        }

        sum += digit;
        isEven = !isEven;
      }

      return sum % 10 === 0;
    },
    message: 'Geçerli bir kredi kartı numarası giriniz',
  },
  {
    name: 'iban',
    validate: (value: string) => {
      const iban = value.replace(/\s/g, '').toUpperCase();

      if (iban.length !== 26) return false;
      if (!/^TR\d{24}$/.test(iban)) return false;

      // IBAN kontrol algoritması
      const rearranged = iban.slice(4) + iban.slice(0, 4);
      const numericString = rearranged.replace(/[A-Z]/g, (char) =>
        (char.charCodeAt(0) - 55).toString(),
      );

      let remainder = '';
      for (let i = 0; i < numericString.length; i++) {
        remainder = remainder + numericString[i];
        if (remainder.length >= 9) {
          remainder = (parseInt(remainder) % 97).toString();
        }
      }

      return parseInt(remainder) % 97 === 1;
    },
    message: 'Geçerli bir IBAN numarası giriniz',
  },
];
```

## Form Validation Composable

### Vue Form Validation

```typescript
// src/core/composables/useValidation.ts
export const useValidation = <T extends Record<string, any>>(
  initialData: T,
  schema: ValidationSchema,
) => {
  const validator = inject<SchemaValidator>('validator');

  if (!validator) {
    throw new Error('Validator service not found');
  }

  const data = reactive<T>({ ...initialData });
  const errors = ref<Record<string, string>>({});
  const isValidating = ref(false);
  const isValid = ref(false);

  const validateField = async (field: keyof T, context?: any) => {
    const fieldRules = schema[field as string];
    if (!fieldRules) return true;

    const fieldSchema = { [field as string]: fieldRules };
    const result = await validator.validate({ [field]: data[field] }, fieldSchema, context);

    if (result.errors.length > 0) {
      errors.value[field as string] = result.errors[0].message;
      return false;
    } else {
      delete errors.value[field as string];
      return true;
    }
  };

  const validateAll = async (context?: any) => {
    isValidating.value = true;

    try {
      const result = await validator.validate(data, schema, context);

      // Hataları temizle
      errors.value = {};

      // Yeni hataları ekle
      result.errors.forEach((error) => {
        errors.value[error.field] = error.message;
      });

      isValid.value = result.isValid;
      return result.isValid;
    } finally {
      isValidating.value = false;
    }
  };

  const clearErrors = () => {
    errors.value = {};
  };

  const clearError = (field: keyof T) => {
    delete errors.value[field as string];
  };

  const setError = (field: keyof T, message: string) => {
    errors.value[field as string] = message;
  };

  const hasError = (field: keyof T) => {
    return !!errors.value[field as string];
  };

  const getError = (field: keyof T) => {
    return errors.value[field as string];
  };

  const reset = () => {
    Object.assign(data, initialData);
    clearErrors();
    isValid.value = false;
  };

  // Reactive validation
  const enableReactiveValidation = (debounceMs: number = 300) => {
    const debouncedValidate = debounce(validateAll, debounceMs);

    watch(
      data,
      () => {
        debouncedValidate();
      },
      { deep: true },
    );
  };

  return {
    data,
    errors: readonly(errors),
    isValidating: readonly(isValidating),
    isValid: readonly(isValid),
    validateField,
    validateAll,
    clearErrors,
    clearError,
    setError,
    hasError,
    getError,
    reset,
    enableReactiveValidation,
  };
};
```

## Business Validation

### İş Kuralları Doğrulayıcısı

```typescript
// src/core/services/validation/business.validator.ts
export interface BusinessRule<T = any> {
  name: string;
  validate: (data: T, context?: any) => Promise<boolean>;
  message: string | ((data: T, context?: any) => string);
}

export interface BusinessValidator {
  addRule<T>(rule: BusinessRule<T>): void;
  validate<T>(data: T, ruleNames: string[], context?: any): Promise<ValidationResult>;
  validateRule<T>(data: T, ruleName: string, context?: any): Promise<boolean>;
}

const createBusinessValidator = (): BusinessValidator => {
  const rules = new Map<string, BusinessRule>();

  return {
    addRule<T>(rule: BusinessRule<T>): void {
      rules.set(rule.name, rule);
    },

    async validate<T>(data: T, ruleNames: string[], context?: any): Promise<ValidationResult> {
      const errors: ValidationError[] = [];

      for (const ruleName of ruleNames) {
        const rule = rules.get(ruleName);
        if (!rule) continue;

        try {
          const isValid = await rule.validate(data, context);
          if (!isValid) {
            const message =
              typeof rule.message === 'function' ? rule.message(data, context) : rule.message;

            errors.push({
              field: ruleName,
              message,
              value: data,
            });
          }
        } catch (error) {
          errors.push({
            field: ruleName,
            message: `İş kuralı hatası: ${error.message}`,
            value: data,
          });
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },

    async validateRule<T>(data: T, ruleName: string, context?: any): Promise<boolean> {
      const rule = rules.get(ruleName);
      if (!rule) return true;

      try {
        return await rule.validate(data, context);
      } catch {
        return false;
      }
    },
  };
};

// İş kuralları örnekleri
export const businessRules: BusinessRule[] = [
  {
    name: 'userCanCreateOrder',
    validate: async (orderData: any, context: any) => {
      const user = context.user;
      const userService = context.userService;

      // Kullanıcının aktif olup olmadığını kontrol et
      if (!user.isActive) return false;

      // Kullanıcının kredi limitini kontrol et
      const creditLimit = await userService.getCreditLimit(user.id);
      return orderData.total <= creditLimit;
    },
    message: 'Kullanıcı sipariş oluşturma yetkisine sahip değil veya kredi limiti yetersiz',
  },
  {
    name: 'inventoryAvailable',
    validate: async (orderData: any, context: any) => {
      const inventoryService = context.inventoryService;

      for (const item of orderData.items) {
        const available = await inventoryService.getAvailableQuantity(item.productId);
        if (available < item.quantity) {
          return false;
        }
      }

      return true;
    },
    message: 'Stokta yeterli ürün bulunmuyor',
  },
  {
    name: 'workingHours',
    validate: async (data: any, context: any) => {
      const now = new Date();
      const hour = now.getHours();

      // Çalışma saatleri: 09:00 - 18:00
      return hour >= 9 && hour < 18;
    },
    message: 'İşlem sadece çalışma saatleri içinde yapılabilir (09:00 - 18:00)',
  },
];
```

## Validation Decorators

### Class Validation Decorators

```typescript
// src/core/decorators/validate.decorator.ts
export function Validate(schema: ValidationSchema, context?: any) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const validator = inject<SchemaValidator>('validator');

      if (validator && args.length > 0) {
        const data = args[0];
        const result = await validator.validate(data, schema, context);

        if (!result.isValid) {
          throw new ValidationError('Validation failed', result.errors);
        }
      }

      return method.apply(this, args);
    };

    return descriptor;
  };
}

// Kullanım örneği:
class UserService {
  @Validate({
    email: [
      { name: 'required', validate: (v) => !!v, message: 'E-posta gerekli' },
      { name: 'email', validate: (v) => /\S+@\S+\.\S+/.test(v), message: 'Geçersiz e-posta' },
    ],
    name: [
      { name: 'required', validate: (v) => !!v, message: 'İsim gerekli' },
      { name: 'minLength', validate: (v, ctx) => v.length >= 2, message: 'En az 2 karakter' },
    ],
  })
  async createUser(userData: CreateUserData): Promise<User> {
    // Validation geçtiyse buraya gelir
    return await this.userRepository.create(userData);
  }
}
```

## Form Components

### Validated Input Component

```vue
<!-- src/shared/components/forms/ValidatedInput.vue -->
<template>
  <div class="validated-input">
    <q-input
      v-model="internalValue"
      :label="label"
      :error="hasError"
      :error-message="errorMessage"
      :loading="isValidating"
      @blur="validateField"
      v-bind="$attrs"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue: any;
  label: string;
  rules: ValidationRule[];
  validateOnBlur?: boolean;
  validateOnInput?: boolean;
  debounceMs?: number;
}

const props = withDefaults(defineProps<Props>(), {
  validateOnBlur: true,
  validateOnInput: false,
  debounceMs: 300,
});

const emit = defineEmits<{
  'update:modelValue': [value: any];
  validation: [isValid: boolean, errors: ValidationError[]];
}>();

const validator = inject<SchemaValidator>('validator');
const internalValue = ref(props.modelValue);
const errors = ref<ValidationError[]>([]);
const isValidating = ref(false);

const hasError = computed(() => errors.value.length > 0);
const errorMessage = computed(() => errors.value[0]?.message || '');

const validateField = async () => {
  if (!validator) return;

  isValidating.value = true;

  try {
    const schema = { field: props.rules };
    const result = await validator.validate({ field: internalValue.value }, schema);

    errors.value = result.errors;
    emit('validation', result.isValid, result.errors);
  } finally {
    isValidating.value = false;
  }
};

const debouncedValidate = debounce(validateField, props.debounceMs);

watch(internalValue, (newValue) => {
  emit('update:modelValue', newValue);

  if (props.validateOnInput) {
    debouncedValidate();
  }
});

watch(
  () => props.modelValue,
  (newValue) => {
    internalValue.value = newValue;
  },
);
</script>
```

### Form Validator Component

```vue
<!-- src/shared/components/forms/FormValidator.vue -->
<template>
  <form @submit.prevent="handleSubmit">
    <slot
      :data="data"
      :errors="errors"
      :isValid="isValid"
      :isValidating="isValidating"
      :validateField="validateField"
      :validateAll="validateAll"
    />
  </form>
</template>

<script setup lang="ts">
interface Props {
  schema: ValidationSchema;
  initialData: Record<string, any>;
  validateOnSubmit?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  validateOnSubmit: true,
});

const emit = defineEmits<{
  submit: [data: any, isValid: boolean];
  validation: [isValid: boolean, errors: Record<string, string>];
}>();

const { data, errors, isValid, isValidating, validateField, validateAll } = useValidation(
  props.initialData,
  props.schema,
);

const handleSubmit = async () => {
  let isFormValid = isValid.value;

  if (props.validateOnSubmit) {
    isFormValid = await validateAll();
  }

  emit('submit', data, isFormValid);
};

watch([isValid, errors], () => {
  emit('validation', isValid.value, errors.value);
});
</script>
```

Bu doğrulama sistemi, hem client-side hem de server-side doğrulama için kapsamlı bir çözüm sunar. Sistem, Vue.js ile entegre olacak şekilde tasarlanmış ve hem basit form doğrulaması hem de karmaşık iş kuralları doğrulaması için kullanılabilir.
