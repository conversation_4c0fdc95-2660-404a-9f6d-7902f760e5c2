<template>
  <q-layout view="lHh Lpr lFf">
    <the-header />
    <the-drawer />

    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import TheHeader from 'src/components/layout/TheHeader.vue';
import TheDrawer from 'src/components/layout/TheDrawer.vue';

/**
 * MainLayout, uygulamanın ana iskeletini oluşturan bir orkestratör bileşendir.
 * Tek sorumluluğu, TheHeader ve TheDrawer gibi yapısal bileşenleri
 * bir araya getirmek ve sayfa içeriğini göstermektir.
 * Kendi içinde durum (state) veya iş mantığı (logic) barındırmaz.
 */
</script>
